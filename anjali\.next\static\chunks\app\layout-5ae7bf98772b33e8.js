(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1650:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(5155),a=s(3554),n=s(1710);function i(){let e=(0,n.Q2)().analytics.googleAnalyticsId;return e&&"G-XXXXXXXXXX"!==e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{src:"https://www.googletagmanager.com/gtag/js?id=".concat(e),strategy:"afterInteractive"}),(0,r.jsx)(a.default,{id:"google-analytics",strategy:"afterInteractive",children:"\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '".concat(e,"', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        ")})]}):null}},1993:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,598,23)),Promise.resolve().then(s.t.bind(s,6253,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,2262)),Promise.resolve().then(s.bind(s,1650)),Promise.resolve().then(s.bind(s,3182)),Promise.resolve().then(s.bind(s,4774))},2262:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var r=s(5155),a=s(2115),n=s(6874),i=s.n(n),o=s(760),l=s(2605),c=s(5684),d=s(488),h=s(9420),m=s(4416),x=s(4783),f=s(285),g=s(1710),u=s(9434),p=s(7509);let w=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Packages",href:"/packages"},{name:"Portfolio",href:"/portfolio"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}];function j(){let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(!1),j=(0,g.Q2)(),v=(0,g.PZ)();(0,a.useEffect)(()=>{let e=()=>{n(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let b=(0,u.ec)(j.contact.whatsapp,j.whatsappMessage);return(0,r.jsxs)("header",{className:(0,u.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",s?"bg-white/95 backdrop-blur-md shadow-md":"bg-white"),children:[(0,r.jsx)("nav",{id:"navigation",className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-display font-bold text-lg",children:"A"})}),(0,r.jsx)("span",{className:"font-display text-xl font-semibold text-text-primary",children:j.site.name})]})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:w.map(e=>(0,r.jsx)(i(),{href:e.href,className:"text-text-primary hover:text-rose-gold-dark px-3 py-2 rounded-md text-sm font-medium transition-colors",children:e.name},e.name))})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i(),{href:v.instagram,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:v.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)(i(),{href:v.tiktok,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,r.jsx)(p.H6Z,{className:"h-5 w-5"})})]}),(0,r.jsx)(f.$,{asChild:!0,variant:"gradient",size:"sm",children:(0,r.jsxs)(i(),{href:b,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"Book Now"]})})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(f.$,{variant:"ghost",size:"icon",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?(0,r.jsx)(m.A,{className:"h-6 w-6"}):(0,r.jsx)(x.A,{className:"h-6 w-6"})})})]})}),(0,r.jsx)(o.N,{children:e&&(0,r.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"md:hidden bg-white border-t border-gray-200",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[w.map(e=>(0,r.jsx)(i(),{href:e.href,className:"text-text-primary hover:text-rose-gold-dark block px-3 py-2 rounded-md text-base font-medium transition-colors",onClick:()=>t(!1),children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4 pb-2 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:v.instagram,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,r.jsx)(c.A,{className:"h-6 w-6"})}),(0,r.jsx)(i(),{href:v.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,r.jsx)(d.A,{className:"h-6 w-6"})})]}),(0,r.jsx)(f.$,{asChild:!0,variant:"gradient",size:"sm",children:(0,r.jsxs)(i(),{href:b,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"Book Now"]})})]})})]})})})]})}},3182:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(5155),a=s(2115),n=s(760),i=s(2605),o=s(9881),l=s(285);function c(){let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=()=>{window.pageYOffset>300?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.jsx)(n.N,{children:e&&(0,r.jsx)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},className:"fixed bottom-8 right-8 z-50",children:(0,r.jsx)(l.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},size:"icon",variant:"gradient",className:"w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300","aria-label":"Scroll to top",children:(0,r.jsx)(o.A,{className:"w-5 h-5"})})})})}},4774:(e,t,s)=>{"use strict";s.d(t,{QueryProvider:()=>l});var r=s(5155),a=s(432),n=s(6715),i=s(192),o=s(2115);function l(e){let{children:t}=e,[s]=(0,o.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnMount:!0,refetchOnReconnect:!0},mutations:{retry:!1}}}));return(0,r.jsxs)(n.Ht,{client:s,children:[t,(0,r.jsx)(i.E,{initialIsOpen:!1})]})}}},e=>{e.O(0,[515,506,277,699,605,967,237,578,441,964,358],()=>e(e.s=1993)),_N_E=e.O()}]);