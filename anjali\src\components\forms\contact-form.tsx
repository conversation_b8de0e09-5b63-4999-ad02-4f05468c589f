'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Send, Phone, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  service: z.string().min(1, 'Please select a service'),
  date: z.string().min(1, 'Please select a preferred date'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
})

type ContactFormData = z.infer<typeof contactSchema>

const services = [
  'Bridal Makeup',
  'Party Makeup',
  'Engagement Makeup',
  'Traditional Makeup',
  'Photoshoot Makeup',
  'Makeup Lessons',
  'Other'
]

export default function ContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const siteConfig = getSiteConfig()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  })

  const onSubmit = async () => {
    setIsSubmitting(true)

    try {
      // Here you would typically send the data to your backend or EmailJS
      // For now, we'll simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 2000))

      setIsSubmitted(true)
      reset()
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    siteConfig.whatsappMessage
  )

  if (isSubmitted) {
    return (
      <Card className="max-w-md mx-auto text-center">
        <CardContent className="pt-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Send className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Message Sent Successfully!
          </h3>
          <p className="text-text-secondary mb-6">
            Thank you for your inquiry. We&apos;ll get back to you within 24 hours.
          </p>
          <Button 
            onClick={() => setIsSubmitted(false)}
            variant="outline"
            className="w-full"
          >
            Send Another Message
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
      {/* Contact Form */}
      <Card>
        <CardHeader>
          <CardTitle className="font-display text-2xl">Send us a Message</CardTitle>
          <CardDescription>
            Fill out the form below and we&apos;ll get back to you as soon as possible.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium text-text-primary">
                  Full Name *
                </label>
                <Input
                  id="name"
                  placeholder="Your full name"
                  {...register('name')}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-red-500 text-xs">{errors.name.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-text-primary">
                  Email Address *
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs">{errors.email.message}</p>
                )}
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="phone" className="text-sm font-medium text-text-primary">
                  Phone Number *
                </label>
                <Input
                  id="phone"
                  placeholder="+977-9800000000"
                  {...register('phone')}
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-red-500 text-xs">{errors.phone.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="date" className="text-sm font-medium text-text-primary">
                  Preferred Date *
                </label>
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                  className={errors.date ? 'border-red-500' : ''}
                />
                {errors.date && (
                  <p className="text-red-500 text-xs">{errors.date.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="service" className="text-sm font-medium text-text-primary">
                Service Interested In *
              </label>
              <select
                id="service"
                {...register('service')}
                className={`flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ${
                  errors.service ? 'border-red-500' : ''
                }`}
              >
                <option value="">Select a service</option>
                {services.map((service) => (
                  <option key={service} value={service}>
                    {service}
                  </option>
                ))}
              </select>
              {errors.service && (
                <p className="text-red-500 text-xs">{errors.service.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="message" className="text-sm font-medium text-text-primary">
                Message *
              </label>
              <Textarea
                id="message"
                placeholder="Tell us about your requirements, occasion, and any specific preferences..."
                rows={4}
                {...register('message')}
                className={errors.message ? 'border-red-500' : ''}
              />
              {errors.message && (
                <p className="text-red-500 text-xs">{errors.message.message}</p>
              )}
            </div>

            <Button
              type="submit"
              variant="gradient"
              size="lg"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Message
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Quick Contact Options */}
      <div className="space-y-6">
        <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
          <CardHeader>
            <CardTitle className="font-display text-xl flex items-center gap-2">
              <Phone className="w-5 h-5 text-rose-gold-dark" />
              Quick Contact
            </CardTitle>
            <CardDescription>
              Need immediate assistance? Contact us directly via WhatsApp or phone.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button asChild variant="gradient" size="lg" className="w-full">
              <a href={whatsappLink} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="w-5 h-5 mr-2" />
                WhatsApp Now
              </a>
            </Button>
            <Button asChild variant="outline" size="lg" className="w-full">
              <a href={`tel:${siteConfig.contact.phone}`}>
                <Phone className="w-5 h-5 mr-2" />
                Call Now
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="font-display text-xl">Business Hours</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Monday - Saturday</span>
              <Badge variant="outline">9:00 AM - 6:00 PM</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Sunday</span>
              <Badge variant="outline">10:00 AM - 4:00 PM</Badge>
            </div>
            <div className="pt-2 border-t border-gray-100">
              <p className="text-sm text-text-muted">
                Emergency bookings available with advance notice
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
