"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[984],{2085:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return u(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,a=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let u=o(t)||o(n);return l[e][u]}),f=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return u(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...f}[t]):({...i,...f})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let n=r(9991),o=r(7102);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},2757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let n=r(6966)._(r(8859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:r}=e,u=e.protocol||"",l=e.pathname||"",i=e.hash||"",a=e.query||"",f=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?f=t+e.host:r&&(f=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(f+=":"+e.port)),a&&"object"==typeof a&&(a=String(n.urlQueryToSearchParams(a)));let c=e.search||a&&"?"+a||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==f?(f="//"+(f||""),l&&"/"!==l[0]&&(l="/"+l)):f||(f=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+u+f+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return u(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},4624:(e,t,r)=>{r.d(t,{DX:()=>l});var n=r(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var u=r(5155),l=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...u}=e;if(n.isValidElement(r)){var l;let e,i,a=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),f=function(e,t){let r={...t};for(let n in t){let o=e[n],u=t[n];/^on[A-Z]/.test(n)?o&&u?r[n]=(...e)=>{let t=u(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...u}:"className"===n&&(r[n]=[o,u].filter(Boolean).join(" "))}return{...e,...r}}(u,r.props);return r.type!==n.Fragment&&(f.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(t,a):a),n.cloneElement(r,f)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,i=n.Children.toArray(o),f=i.find(a);if(f){let e=f.props.children,o=i.map(t=>t!==f?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}("Slot"),i=Symbol("radix.slottable");function a(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=u(e,n)),t&&(o.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return b}});let n=r(6966),o=r(5155),u=n._(r(2115)),l=r(2757),i=r(5227),a=r(9818),f=r(6654),c=r(9991),s=r(5929);r(3230);let p=r(4930),d=r(2664),y=r(6634);function h(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function g(e){let t,r,n,[l,g]=(0,u.useOptimistic)(p.IDLE_LINK_STATUS),b=(0,u.useRef)(null),{href:v,as:P,children:_,prefetch:O=null,passHref:E,replace:j,shallow:C,scroll:N,onClick:S,onMouseEnter:x,onTouchStart:A,legacyBehavior:T=!1,onNavigate:R,ref:L,unstable_dynamicOnHover:w,...M}=e;t=_,T&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let I=u.default.useContext(i.AppRouterContext),U=!1!==O,k=null===O||"auto"===O?a.PrefetchKind.AUTO:a.PrefetchKind.FULL,{href:D,as:F}=u.default.useMemo(()=>{let e=h(v);return{href:e,as:P?h(P):e}},[v,P]);T&&(r=u.default.Children.only(t));let K=T?r&&"object"==typeof r&&r.ref:L,B=u.default.useCallback(e=>(null!==I&&(b.current=(0,p.mountLinkInstance)(e,D,I,k,U,g)),()=>{b.current&&((0,p.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,p.unmountPrefetchableInstance)(e)}),[U,D,I,k,g]),V={ref:(0,f.useMergedRef)(B,K),onClick(e){T||"function"!=typeof S||S(e),T&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,o,l,i){let{nodeName:a}=e.currentTarget;if(!("A"===a.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}u.default.startTransition(()=>{(0,y.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,D,F,b,j,N,R))},onMouseEnter(e){T||"function"!=typeof x||x(e),T&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&U&&(0,p.onNavigationIntent)(e.currentTarget,!0===w)},onTouchStart:function(e){T||"function"!=typeof A||A(e),T&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&U&&(0,p.onNavigationIntent)(e.currentTarget,!0===w)}};return(0,c.isAbsoluteUrl)(F)?V.href=F:T&&!E&&("a"!==r.type||"href"in r.props)||(V.href=(0,s.addBasePath)(F)),n=T?u.default.cloneElement(r,V):(0,o.jsx)("a",{...M,...V,children:t}),(0,o.jsx)(m.Provider,{value:l,children:n})}r(3180);let m=(0,u.createContext)(p.IDLE_LINK_STATUS),b=()=>(0,u.useContext)(m);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function u(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return y},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return m},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return a},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return u},isResSent:function(){return f},loadGetInitialProps:function(){return s},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function a(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function f(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function s(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await s(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&f(r))return n;if(!n)throw Object.defineProperty(Error('"'+a(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class y extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);