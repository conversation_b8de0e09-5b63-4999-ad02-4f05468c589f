'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowR<PERSON>, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { ImageModal } from '@/components/ui/image-modal'
import { useFeaturedGallery } from '@/hooks/use-api'

// Fallback gallery data when API is not available
const fallbackGallery = [
  {
    id: 'fallback-1',
    title: 'Bridal Elegance',
    description: 'Classic bridal makeup with timeless elegance',
    image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Bridal',
    tags: ['bridal', 'elegant', 'classic'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-2',
    title: 'Glamorous Evening Look',
    description: 'Bold and glamorous makeup for special events',
    image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Party',
    tags: ['glamour', 'evening', 'bold'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-3',
    title: 'Natural Beauty',
    description: 'Enhancing natural features with subtle makeup',
    image: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Natural',
    tags: ['natural', 'subtle', 'everyday'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-4',
    title: 'Smokey Eyes',
    description: 'Dramatic smokey eye makeup for evening events',
    image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Evening',
    tags: ['smokey', 'dramatic', 'eyes'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-5',
    title: 'Traditional Look',
    description: 'Beautiful traditional makeup for cultural events',
    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Traditional',
    tags: ['traditional', 'cultural', 'heritage'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-6',
    title: 'Soft Glam',
    description: 'Soft and romantic makeup perfect for any occasion',
    image: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Soft Glam',
    tags: ['soft', 'romantic', 'versatile'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-7',
    title: 'Bold & Beautiful',
    description: 'Statement makeup with bold colors and dramatic flair',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Bold',
    tags: ['bold', 'colorful', 'statement'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-8',
    title: 'Vintage Charm',
    description: 'Classic vintage-inspired makeup with timeless appeal',
    image: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=400&fit=crop&crop=face&q=80',
    category: 'Vintage',
    tags: ['vintage', 'classic', 'retro'],
    featured: true,
    status: 'ACTIVE' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export default function GalleryShowcase() {
  const { data: galleryData } = useFeaturedGallery(8)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Use API data if available, otherwise use fallback gallery
  const featuredItems = (galleryData?.gallery && galleryData.gallery.length > 0)
    ? galleryData.gallery
    : fallbackGallery

  const isUsingFallback = !galleryData?.gallery || galleryData.gallery.length === 0

  // Modal handlers
  const openModal = (index: number) => {
    setCurrentImageIndex(index)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  // Always show content - either from API or fallback

  return (
    <Section id="gallery">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Work"
          title="Portfolio Gallery"
          description={isUsingFallback
            ? "Explore our stunning portfolio showcasing diverse makeup styles and transformations for various occasions. (Sample gallery - connect to CMS for live portfolio)"
            : "Explore our stunning portfolio showcasing diverse makeup styles and transformations for various occasions."
          }
        />
      </AnimatedElement>

      <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {featuredItems.map((item, index) => (
          <StaggeredItem key={item.id}>
            <div
              className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer"
              onClick={() => openModal(index)}
            >
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  {item.category && (
                    <Badge variant="secondary" className="mb-2 text-xs">
                      {item.category}
                    </Badge>
                  )}
                  <h3 className="font-display text-lg font-semibold mb-1">
                    {item.title}
                  </h3>
                  {item.description && (
                    <p className="text-sm text-white/80">
                      {item.description}
                    </p>
                  )}
                </div>
              </div>

              {/* View Icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Eye className="w-5 h-5 text-white" />
              </div>

              {/* Tags */}
              <div className="absolute top-4 left-4 flex flex-wrap gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {item.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Gallery Grid Layout for larger screens */}
      <div className="hidden lg:block mb-12">
        <StaggeredContainer className="grid grid-cols-4 grid-rows-2 gap-4 h-96">
          {featuredItems.slice(0, 6).map((item, index) => {
            const spanClasses = [
              'col-span-2 row-span-2', // Large
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-2', // Tall
              'col-span-1 row-span-1', // Small
              'col-span-1 row-span-1', // Small
            ]
            
            return (
              <StaggeredItem key={`grid-${item.id}`}>
                <div
                  className={`group relative overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer ${spanClasses[index]}`}
                  onClick={() => openModal(index)}
                >
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {item.category}
                      </Badge>
                      <h3 className="font-display text-sm font-semibold mb-1">
                        {item.title}
                      </h3>
                    </div>
                  </div>

                  {/* View Icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                </div>
              </StaggeredItem>
            )
          })}
        </StaggeredContainer>
      </div>

      <AnimatedElement animation="slideUp" delay={0.6} className="text-center">
        <Button asChild variant="gradient" size="lg">
          <Link href="/portfolio">
            View Full Portfolio
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </Button>
      </AnimatedElement>

      {/* Image Modal */}
      <ImageModal
        isOpen={isModalOpen}
        onClose={closeModal}
        images={featuredItems}
        currentIndex={currentImageIndex}
        onIndexChange={setCurrentImageIndex}
      />
    </Section>
  )
}
