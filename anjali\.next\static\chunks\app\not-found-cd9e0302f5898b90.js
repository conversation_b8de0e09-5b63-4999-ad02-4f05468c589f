(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(5155),a=r(2115),o=r(4624),l=r(2085),n=r(9434);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-rose-gold text-white shadow hover:bg-rose-gold-dark",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-600",outline:"border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white",secondary:"bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark",ghost:"hover:bg-rose-gold-light hover:text-rose-gold-dark",link:"text-rose-gold-dark underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:i=!1,...c}=e,x=i?o.DX:"button";return(0,s.jsx)(x,{className:(0,n.cn)(d({variant:a,size:l,className:r})),ref:t,...c})});i.displayName="Button"},1070:(e,t,r)=>{Promise.resolve().then(r.bind(r,7003))},5169:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>n});var s=r(5155),a=r(2115),o=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md",r),...a})});l.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("font-display text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-text-secondary",r),...a})});i.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},7003:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(5155),a=r(6874),o=r.n(a),l=r(7340);let n=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var d=r(5169),i=r(285),c=r(6695);function x(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-cream via-white to-blush-pink-light px-4",children:(0,s.jsxs)("div",{className:"text-center space-y-8 max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"text-8xl md:text-9xl font-bold text-rose-gold/20 select-none",children:"404"}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-display font-bold text-3xl",children:"A"})})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h1",{className:"font-display text-3xl md:text-4xl font-bold text-text-primary",children:"Page Not Found"}),(0,s.jsx)("p",{className:"text-text-secondary leading-relaxed",children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(i.$,{asChild:!0,variant:"gradient",className:"group",children:(0,s.jsxs)(o(),{href:"/",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})}),(0,s.jsx)(i.$,{asChild:!0,variant:"outline",children:(0,s.jsxs)(o(),{href:"/services",children:[(0,s.jsx)(n,{className:"w-4 h-4 mr-2"}),"Browse Services"]})})]}),(0,s.jsx)(c.Zp,{className:"bg-white/80 backdrop-blur-sm border-0",children:(0,s.jsxs)(c.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"font-semibold text-text-primary mb-4",children:"Popular Pages"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,s.jsx)(o(),{href:"/about",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"About Us"}),(0,s.jsx)(o(),{href:"/services",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"Services"}),(0,s.jsx)(o(),{href:"/packages",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"Packages"}),(0,s.jsx)(o(),{href:"/portfolio",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"Portfolio"}),(0,s.jsx)(o(),{href:"/blog",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"Blog"}),(0,s.jsx)(o(),{href:"/contact",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:"Contact"})]})]})}),(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>window.history.back(),className:"text-text-muted hover:text-text-secondary",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Go Back"]})]})})}},7340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{$g:()=>l,Yq:()=>n,cn:()=>o,ec:()=>d});var s=r(2596),a=r(9688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function l(e){return e.replace(/NPR\s*/g,"NPR ")}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,t){let r=e.replace(/[^\d+]/g,""),s=encodeURIComponent(t);return"https://wa.me/".concat(r,"?text=").concat(s)}}},e=>{e.O(0,[277,699,441,964,358],()=>e(e.s=1070)),_N_E=e.O()}]);