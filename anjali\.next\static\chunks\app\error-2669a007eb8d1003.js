(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(5155),a=r(2115),n=r(4624),l=r(2085),d=r(9434);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-rose-gold text-white shadow hover:bg-rose-gold-dark",destructive:"bg-red-500 text-white shadow-sm hover:bg-red-600",outline:"border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white",secondary:"bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark",ghost:"hover:bg-rose-gold-light hover:text-rose-gold-dark",link:"text-rose-gold-dark underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:o=!1,...c}=e,h=o?n.DX:"button";return(0,s.jsx)(h,{className:(0,d.cn)(i({variant:a,size:l,className:r})),ref:t,...c})});o.displayName="Button"},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>d});var s=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md",r),...a})});l.displayName="Card";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});d.displayName="CardHeader";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("font-display text-2xl font-semibold leading-none tracking-tight",r),...a})});i.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-text-secondary",r),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},6796:(e,t,r)=>{Promise.resolve().then(r.bind(r,7550))},7340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7550:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(5155),a=r(2115),n=r(9946);let l=(0,n.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),d=(0,n.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var i=r(7340),o=r(285),c=r(6695),h=r(6874),x=r.n(h);function m(e){let{error:t,reset:r}=e;return(0,a.useEffect)(()=>{console.error("Application error:",t)},[t]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-cream via-white to-blush-pink-light px-4",children:(0,s.jsxs)("div",{className:"text-center space-y-8 max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto",children:(0,s.jsx)(l,{className:"w-12 h-12 text-white"})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h1",{className:"font-display text-3xl md:text-4xl font-bold text-text-primary",children:"Something went wrong!"}),(0,s.jsx)("p",{className:"text-text-secondary leading-relaxed",children:"We're sorry, but something unexpected happened. Please try refreshing the page or contact us if the problem persists."}),!1]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(o.$,{onClick:r,variant:"gradient",className:"group",children:[(0,s.jsx)(d,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,s.jsx)(o.$,{asChild:!0,variant:"outline",children:(0,s.jsxs)(x(),{href:"/",children:[(0,s.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})})]}),(0,s.jsx)(c.Zp,{className:"bg-white/80 backdrop-blur-sm border-0",children:(0,s.jsxs)(c.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"font-semibold text-text-primary mb-4",children:"Need Help?"}),(0,s.jsx)("p",{className:"text-text-secondary text-sm mb-4",children:"If this error continues, please contact us and we'll help resolve the issue."}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-text-secondary",children:"WhatsApp:"}),(0,s.jsx)("span",{className:"text-text-primary",children:"+977-9800000000"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-text-secondary",children:"Email:"}),(0,s.jsx)("span",{className:"text-text-primary",children:"<EMAIL>"})]})]})]})})]})})}},9434:(e,t,r)=>{"use strict";r.d(t,{$g:()=>l,Yq:()=>d,cn:()=>n,ec:()=>i});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function l(e){return e.replace(/NPR\s*/g,"NPR ")}function d(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function i(e,t){let r=e.replace(/[^\d+]/g,""),s=encodeURIComponent(t);return"https://wa.me/".concat(r,"?text=").concat(s)}}},e=>{e.O(0,[277,699,441,964,358],()=>e(e.s=6796)),_N_E=e.O()}]);