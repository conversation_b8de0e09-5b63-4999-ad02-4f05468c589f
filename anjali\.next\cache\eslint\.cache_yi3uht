[{"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\[slug]\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\contact\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\error.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\loading.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\manifest.ts": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\not-found.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\packages\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\portfolio\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\robots.ts": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\services\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\sitemap.ts": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\forms\\contact-form.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\footer.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\header.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cities.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cta.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-experience.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-hero.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-story.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-cta.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-grid.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-hero.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-post-content.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-form-section.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-hero.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-info.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-section.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\gallery-showcase.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\hero.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-cta.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-grid.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-hero.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-preview.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-cta.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-gallery.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-hero.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-cta.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-grid.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-hero.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-overview.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\testimonials.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\analytics.tsx": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\json-ld.tsx": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\page-seo.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\animated-element.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\badge.tsx": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\button.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\card.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\error-boundary.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\image-modal.tsx": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\input.tsx": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading-skeleton.tsx": "56", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading.tsx": "57", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\retry-wrapper.tsx": "58", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\scroll-to-top.tsx": "59", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\section.tsx": "60", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\skip-links.tsx": "61", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\textarea.tsx": "62", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\hooks\\use-api.ts": "63", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\api-client.ts": "64", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\data.ts": "65", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\seo.ts": "66", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\utils.ts": "67", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\providers\\query-provider.tsx": "68", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\services\\api.ts": "69", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\types\\api.ts": "70"}, {"size": 494, "mtime": 1752802120461, "results": "71", "hashOfConfig": "72"}, {"size": 303, "mtime": 1752802097065, "results": "73", "hashOfConfig": "72"}, {"size": 984, "mtime": 1752802024811, "results": "74", "hashOfConfig": "72"}, {"size": 358, "mtime": 1752802163279, "results": "75", "hashOfConfig": "72"}, {"size": 3263, "mtime": 1752725130297, "results": "76", "hashOfConfig": "72"}, {"size": 2855, "mtime": 1752636386518, "results": "77", "hashOfConfig": "72"}, {"size": 118, "mtime": 1752631801766, "results": "78", "hashOfConfig": "72"}, {"size": 2272, "mtime": 1752733627758, "results": "79", "hashOfConfig": "72"}, {"size": 3883, "mtime": 1752802295066, "results": "80", "hashOfConfig": "72"}, {"size": 343, "mtime": 1752802151627, "results": "81", "hashOfConfig": "72"}, {"size": 606, "mtime": 1752802109132, "results": "82", "hashOfConfig": "72"}, {"size": 362, "mtime": 1752802131013, "results": "83", "hashOfConfig": "72"}, {"size": 831, "mtime": 1752724658013, "results": "84", "hashOfConfig": "72"}, {"size": 343, "mtime": 1752802141394, "results": "85", "hashOfConfig": "72"}, {"size": 1669, "mtime": 1752725009012, "results": "86", "hashOfConfig": "72"}, {"size": 10374, "mtime": 1752802424652, "results": "87", "hashOfConfig": "72"}, {"size": 7112, "mtime": 1752630411600, "results": "88", "hashOfConfig": "72"}, {"size": 6816, "mtime": 1752725396687, "results": "89", "hashOfConfig": "72"}, {"size": 6567, "mtime": 1752801561998, "results": "90", "hashOfConfig": "72"}, {"size": 7306, "mtime": 1752725130308, "results": "91", "hashOfConfig": "72"}, {"size": 8528, "mtime": 1752630680961, "results": "92", "hashOfConfig": "72"}, {"size": 5663, "mtime": 1752630613051, "results": "93", "hashOfConfig": "72"}, {"size": 6291, "mtime": 1752801576926, "results": "94", "hashOfConfig": "72"}, {"size": 6833, "mtime": 1752631210590, "results": "95", "hashOfConfig": "72"}, {"size": 13621, "mtime": 1752801622860, "results": "96", "hashOfConfig": "72"}, {"size": 3456, "mtime": 1752631137486, "results": "97", "hashOfConfig": "72"}, {"size": 9906, "mtime": 1752631266732, "results": "98", "hashOfConfig": "72"}, {"size": 783, "mtime": 1752631349874, "results": "99", "hashOfConfig": "72"}, {"size": 3481, "mtime": 1752725130312, "results": "100", "hashOfConfig": "72"}, {"size": 10128, "mtime": 1752725130313, "results": "101", "hashOfConfig": "72"}, {"size": 746, "mtime": 1752630218135, "results": "102", "hashOfConfig": "72"}, {"size": 10542, "mtime": 1752724383823, "results": "103", "hashOfConfig": "72"}, {"size": 8115, "mtime": 1752630424162, "results": "104", "hashOfConfig": "72"}, {"size": 7093, "mtime": 1752725130316, "results": "105", "hashOfConfig": "72"}, {"size": 12461, "mtime": 1752801635912, "results": "106", "hashOfConfig": "72"}, {"size": 3454, "mtime": 1752630892026, "results": "107", "hashOfConfig": "72"}, {"size": 9009, "mtime": 1752801647931, "results": "108", "hashOfConfig": "72"}, {"size": 7042, "mtime": 1752725130331, "results": "109", "hashOfConfig": "72"}, {"size": 8132, "mtime": 1752724478795, "results": "110", "hashOfConfig": "72"}, {"size": 3387, "mtime": 1752631015554, "results": "111", "hashOfConfig": "72"}, {"size": 5595, "mtime": 1752725130333, "results": "112", "hashOfConfig": "72"}, {"size": 10606, "mtime": 1752801674458, "results": "113", "hashOfConfig": "72"}, {"size": 3423, "mtime": 1752630792387, "results": "114", "hashOfConfig": "72"}, {"size": 4389, "mtime": 1752801687295, "results": "115", "hashOfConfig": "72"}, {"size": 13118, "mtime": 1752725968851, "results": "116", "hashOfConfig": "72"}, {"size": 908, "mtime": 1752631469701, "results": "117", "hashOfConfig": "72"}, {"size": 6143, "mtime": 1752725477786, "results": "118", "hashOfConfig": "72"}, {"size": 5062, "mtime": 1752724686210, "results": "119", "hashOfConfig": "72"}, {"size": 2581, "mtime": 1752630003026, "results": "120", "hashOfConfig": "72"}, {"size": 1375, "mtime": 1752736508725, "results": "121", "hashOfConfig": "72"}, {"size": 2048, "mtime": 1752730793498, "results": "122", "hashOfConfig": "72"}, {"size": 1903, "mtime": 1752735770087, "results": "123", "hashOfConfig": "72"}, {"size": 3382, "mtime": 1752637321346, "results": "124", "hashOfConfig": "72"}, {"size": 7212, "mtime": 1752801863141, "results": "125", "hashOfConfig": "72"}, {"size": 848, "mtime": 1752736536295, "results": "126", "hashOfConfig": "72"}, {"size": 4573, "mtime": 1752725286038, "results": "127", "hashOfConfig": "72"}, {"size": 2145, "mtime": 1752631792628, "results": "128", "hashOfConfig": "72"}, {"size": 4793, "mtime": 1752637378512, "results": "129", "hashOfConfig": "72"}, {"size": 1414, "mtime": 1752631855457, "results": "130", "hashOfConfig": "72"}, {"size": 1596, "mtime": 1752736591051, "results": "131", "hashOfConfig": "72"}, {"size": 580, "mtime": 1752631892952, "results": "132", "hashOfConfig": "72"}, {"size": 756, "mtime": 1752736547862, "results": "133", "hashOfConfig": "72"}, {"size": 11825, "mtime": 1752636357720, "results": "134", "hashOfConfig": "72"}, {"size": 2331, "mtime": 1752725352347, "results": "135", "hashOfConfig": "72"}, {"size": 5530, "mtime": 1752630317122, "results": "136", "hashOfConfig": "72"}, {"size": 7817, "mtime": 1752725225246, "results": "137", "hashOfConfig": "72"}, {"size": 4137, "mtime": 1752725365475, "results": "138", "hashOfConfig": "72"}, {"size": 1377, "mtime": 1752725377401, "results": "139", "hashOfConfig": "72"}, {"size": 8766, "mtime": 1752636307379, "results": "140", "hashOfConfig": "72"}, {"size": 3472, "mtime": 1752636261135, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j1wvi9", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\[slug]\\page.tsx", [], ["352"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\manifest.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\packages\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\portfolio\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\forms\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cities.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-story.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-post-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-form-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-info.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\gallery-showcase.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-gallery.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-overview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\testimonials.tsx", [], ["353", "354", "355", "356"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\analytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\json-ld.tsx", [], ["357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\page-seo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\animated-element.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\image-modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\retry-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\scroll-to-top.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\skip-links.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\hooks\\use-api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\api-client.ts", [], ["369", "370", "371", "372"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\seo.ts", [], ["373", "374", "375", "376", "377", "378", "379"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\utils.ts", [], ["380", "381", "382"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\providers\\query-provider.tsx", [], ["383"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\types\\api.ts", [], [], {"ruleId": "384", "severity": 2, "message": "385", "line": 35, "column": 41, "nodeType": "386", "messageId": "387", "endLine": 35, "endColumn": 44, "suggestions": "388", "suppressions": "389"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 145, "column": 41, "nodeType": "386", "messageId": "387", "endLine": 145, "endColumn": 44, "suggestions": "390", "suppressions": "391"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 149, "column": 47, "nodeType": "386", "messageId": "387", "endLine": 149, "endColumn": 50, "suggestions": "392", "suppressions": "393"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 235, "column": 41, "nodeType": "386", "messageId": "387", "endLine": 235, "endColumn": 44, "suggestions": "394", "suppressions": "395"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 239, "column": 47, "nodeType": "386", "messageId": "387", "endLine": 239, "endColumn": 50, "suggestions": "396", "suppressions": "397"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 150, "column": 30, "nodeType": "386", "messageId": "387", "endLine": 150, "endColumn": 33, "suggestions": "398", "suppressions": "399"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 151, "column": 33, "nodeType": "386", "messageId": "387", "endLine": 151, "endColumn": 36, "suggestions": "400", "suppressions": "401"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 154, "column": 28, "nodeType": "386", "messageId": "387", "endLine": 154, "endColumn": 31, "suggestions": "402", "suppressions": "403"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 164, "column": 35, "nodeType": "386", "messageId": "387", "endLine": 164, "endColumn": 38, "suggestions": "404", "suppressions": "405"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 165, "column": 34, "nodeType": "386", "messageId": "387", "endLine": 165, "endColumn": 37, "suggestions": "406", "suppressions": "407"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 166, "column": 27, "nodeType": "386", "messageId": "387", "endLine": 166, "endColumn": 30, "suggestions": "408", "suppressions": "409"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 166, "column": 74, "nodeType": "386", "messageId": "387", "endLine": 166, "endColumn": 77, "suggestions": "410", "suppressions": "411"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 167, "column": 56, "nodeType": "386", "messageId": "387", "endLine": 167, "endColumn": 59, "suggestions": "412", "suppressions": "413"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 170, "column": 60, "nodeType": "386", "messageId": "387", "endLine": 170, "endColumn": 63, "suggestions": "414", "suppressions": "415"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 172, "column": 30, "nodeType": "386", "messageId": "387", "endLine": 172, "endColumn": 33, "suggestions": "416", "suppressions": "417"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 173, "column": 36, "nodeType": "386", "messageId": "387", "endLine": 173, "endColumn": 39, "suggestions": "418", "suppressions": "419"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 174, "column": 31, "nodeType": "386", "messageId": "387", "endLine": 174, "endColumn": 34, "suggestions": "420", "suppressions": "421"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 9, "column": 19, "nodeType": "386", "messageId": "387", "endLine": 9, "endColumn": 22, "suggestions": "422", "suppressions": "423"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 70, "column": 38, "nodeType": "386", "messageId": "387", "endLine": 70, "endColumn": 41, "suggestions": "424", "suppressions": "425"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 75, "column": 37, "nodeType": "386", "messageId": "387", "endLine": 75, "endColumn": 40, "suggestions": "426", "suppressions": "427"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 85, "column": 57, "nodeType": "386", "messageId": "387", "endLine": 85, "endColumn": 60, "suggestions": "428", "suppressions": "429"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 179, "column": 26, "nodeType": "386", "messageId": "387", "endLine": 179, "endColumn": 29, "suggestions": "430", "suppressions": "431"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 183, "column": 33, "nodeType": "386", "messageId": "387", "endLine": 183, "endColumn": 36, "suggestions": "432", "suppressions": "433"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 186, "column": 30, "nodeType": "386", "messageId": "387", "endLine": 186, "endColumn": 33, "suggestions": "434", "suppressions": "435"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 187, "column": 33, "nodeType": "386", "messageId": "387", "endLine": 187, "endColumn": 36, "suggestions": "436", "suppressions": "437"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 194, "column": 24, "nodeType": "386", "messageId": "387", "endLine": 194, "endColumn": 27, "suggestions": "438", "suppressions": "439"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 195, "column": 31, "nodeType": "386", "messageId": "387", "endLine": 195, "endColumn": 34, "suggestions": "440", "suppressions": "441"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 200, "column": 57, "nodeType": "386", "messageId": "387", "endLine": 200, "endColumn": 60, "suggestions": "442", "suppressions": "443"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 59, "column": 46, "nodeType": "386", "messageId": "387", "endLine": 59, "endColumn": 49, "suggestions": "444", "suppressions": "445"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 59, "column": 56, "nodeType": "386", "messageId": "387", "endLine": 59, "endColumn": 59, "suggestions": "446", "suppressions": "447"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 100, "column": 93, "nodeType": "386", "messageId": "387", "endLine": 100, "endColumn": 96, "suggestions": "448", "suppressions": "449"}, {"ruleId": "384", "severity": 2, "message": "385", "line": 18, "column": 42, "nodeType": "386", "messageId": "387", "endLine": 18, "endColumn": 45, "suggestions": "450", "suppressions": "451"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["452", "453"], ["454"], ["455", "456"], ["457"], ["458", "459"], ["460"], ["461", "462"], ["463"], ["464", "465"], ["466"], ["467", "468"], ["469"], ["470", "471"], ["472"], ["473", "474"], ["475"], ["476", "477"], ["478"], ["479", "480"], ["481"], ["482", "483"], ["484"], ["485", "486"], ["487"], ["488", "489"], ["490"], ["491", "492"], ["493"], ["494", "495"], ["496"], ["497", "498"], ["499"], ["500", "501"], ["502"], ["503", "504"], ["505"], ["506", "507"], ["508"], ["509", "510"], ["511"], ["512", "513"], ["514"], ["515", "516"], ["517"], ["518", "519"], ["520"], ["521", "522"], ["523"], ["524", "525"], ["526"], ["527", "528"], ["529"], ["530", "531"], ["532"], ["533", "534"], ["535"], ["536", "537"], ["538"], ["539", "540"], ["541"], ["542", "543"], ["544"], ["545", "546"], ["547"], {"messageId": "548", "fix": "549", "desc": "550"}, {"messageId": "551", "fix": "552", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "556", "desc": "550"}, {"messageId": "551", "fix": "557", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "558", "desc": "550"}, {"messageId": "551", "fix": "559", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "560", "desc": "550"}, {"messageId": "551", "fix": "561", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "562", "desc": "550"}, {"messageId": "551", "fix": "563", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "564", "desc": "550"}, {"messageId": "551", "fix": "565", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "566", "desc": "550"}, {"messageId": "551", "fix": "567", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "568", "desc": "550"}, {"messageId": "551", "fix": "569", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "570", "desc": "550"}, {"messageId": "551", "fix": "571", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "572", "desc": "550"}, {"messageId": "551", "fix": "573", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "574", "desc": "550"}, {"messageId": "551", "fix": "575", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "576", "desc": "550"}, {"messageId": "551", "fix": "577", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "578", "desc": "550"}, {"messageId": "551", "fix": "579", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "580", "desc": "550"}, {"messageId": "551", "fix": "581", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "582", "desc": "550"}, {"messageId": "551", "fix": "583", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "584", "desc": "550"}, {"messageId": "551", "fix": "585", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "586", "desc": "550"}, {"messageId": "551", "fix": "587", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "588", "desc": "550"}, {"messageId": "551", "fix": "589", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "590", "desc": "550"}, {"messageId": "551", "fix": "591", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "592", "desc": "550"}, {"messageId": "551", "fix": "593", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "594", "desc": "550"}, {"messageId": "551", "fix": "595", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "596", "desc": "550"}, {"messageId": "551", "fix": "597", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "598", "desc": "550"}, {"messageId": "551", "fix": "599", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "600", "desc": "550"}, {"messageId": "551", "fix": "601", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "602", "desc": "550"}, {"messageId": "551", "fix": "603", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "604", "desc": "550"}, {"messageId": "551", "fix": "605", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "606", "desc": "550"}, {"messageId": "551", "fix": "607", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "608", "desc": "550"}, {"messageId": "551", "fix": "609", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "610", "desc": "550"}, {"messageId": "551", "fix": "611", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "612", "desc": "550"}, {"messageId": "551", "fix": "613", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "614", "desc": "550"}, {"messageId": "551", "fix": "615", "desc": "553"}, {"kind": "554", "justification": "555"}, {"messageId": "548", "fix": "616", "desc": "550"}, {"messageId": "551", "fix": "617", "desc": "553"}, {"kind": "554", "justification": "555"}, "suggestUnknown", {"range": "618", "text": "619"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "620", "text": "621"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "622", "text": "619"}, {"range": "623", "text": "621"}, {"range": "624", "text": "619"}, {"range": "625", "text": "621"}, {"range": "626", "text": "619"}, {"range": "627", "text": "621"}, {"range": "628", "text": "619"}, {"range": "629", "text": "621"}, {"range": "630", "text": "619"}, {"range": "631", "text": "621"}, {"range": "632", "text": "619"}, {"range": "633", "text": "621"}, {"range": "634", "text": "619"}, {"range": "635", "text": "621"}, {"range": "636", "text": "619"}, {"range": "637", "text": "621"}, {"range": "638", "text": "619"}, {"range": "639", "text": "621"}, {"range": "640", "text": "619"}, {"range": "641", "text": "621"}, {"range": "642", "text": "619"}, {"range": "643", "text": "621"}, {"range": "644", "text": "619"}, {"range": "645", "text": "621"}, {"range": "646", "text": "619"}, {"range": "647", "text": "621"}, {"range": "648", "text": "619"}, {"range": "649", "text": "621"}, {"range": "650", "text": "619"}, {"range": "651", "text": "621"}, {"range": "652", "text": "619"}, {"range": "653", "text": "621"}, {"range": "654", "text": "619"}, {"range": "655", "text": "621"}, {"range": "656", "text": "619"}, {"range": "657", "text": "621"}, {"range": "658", "text": "619"}, {"range": "659", "text": "621"}, {"range": "660", "text": "619"}, {"range": "661", "text": "621"}, {"range": "662", "text": "619"}, {"range": "663", "text": "621"}, {"range": "664", "text": "619"}, {"range": "665", "text": "621"}, {"range": "666", "text": "619"}, {"range": "667", "text": "621"}, {"range": "668", "text": "619"}, {"range": "669", "text": "621"}, {"range": "670", "text": "619"}, {"range": "671", "text": "621"}, {"range": "672", "text": "619"}, {"range": "673", "text": "621"}, {"range": "674", "text": "619"}, {"range": "675", "text": "621"}, {"range": "676", "text": "619"}, {"range": "677", "text": "621"}, {"range": "678", "text": "619"}, {"range": "679", "text": "621"}, {"range": "680", "text": "619"}, {"range": "681", "text": "621"}, {"range": "682", "text": "619"}, {"range": "683", "text": "621"}, [974, 977], "unknown", [974, 977], "never", [6455, 6458], [6455, 6458], [6737, 6740], [6737, 6740], [10621, 10624], [10621, 10624], [10912, 10915], [10912, 10915], [4887, 4890], [4887, 4890], [4931, 4934], [4931, 4934], [5023, 5026], [5023, 5026], [5327, 5330], [5327, 5330], [5378, 5381], [5378, 5381], [5420, 5423], [5420, 5423], [5467, 5470], [5467, 5470], [5587, 5590], [5587, 5590], [5721, 5724], [5721, 5724], [5775, 5778], [5775, 5778], [5841, 5844], [5841, 5844], [5886, 5889], [5886, 5889], [326, 329], [326, 329], [1546, 1549], [1546, 1549], [1703, 1706], [1703, 1706], [1995, 1998], [1995, 1998], [6002, 6005], [6002, 6005], [6111, 6114], [6111, 6114], [6191, 6194], [6191, 6194], [6237, 6240], [6237, 6240], [6385, 6388], [6385, 6388], [6426, 6429], [6426, 6429], [6604, 6607], [6604, 6607], [1745, 1748], [1745, 1748], [1755, 1758], [1755, 1758], [3003, 3006], [3003, 3006], [735, 738], [735, 738]]