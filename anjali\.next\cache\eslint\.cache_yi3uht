[{"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\[slug]\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\contact\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\error.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\loading.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\manifest.ts": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\not-found.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\packages\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\portfolio\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\robots.ts": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\services\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\sitemap.ts": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\forms\\contact-form.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\footer.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\header.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cities.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cta.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-experience.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-hero.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-story.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-cta.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-grid.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-hero.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-post-content.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-form-section.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-hero.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-info.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-section.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\gallery-showcase.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\hero.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-cta.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-grid.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-hero.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-preview.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-cta.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-gallery.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-hero.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-cta.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-grid.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-hero.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-overview.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\testimonials.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\analytics.tsx": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\json-ld.tsx": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\page-seo.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\animated-element.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\badge.tsx": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\button.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\card.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\error-boundary.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\image-modal.tsx": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\input.tsx": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading-skeleton.tsx": "56", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading.tsx": "57", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\retry-wrapper.tsx": "58", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\scroll-to-top.tsx": "59", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\section.tsx": "60", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\skip-links.tsx": "61", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\textarea.tsx": "62", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\hooks\\use-api.ts": "63", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\api-client.ts": "64", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\data.ts": "65", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\seo.ts": "66", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\utils.ts": "67", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\providers\\query-provider.tsx": "68", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\services\\api.ts": "69", "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\types\\api.ts": "70"}, {"size": 535, "mtime": 1752737940805, "results": "71", "hashOfConfig": "72"}, {"size": 343, "mtime": 1752737979398, "results": "73", "hashOfConfig": "72"}, {"size": 1024, "mtime": 1752738221214, "results": "74", "hashOfConfig": "72"}, {"size": 398, "mtime": 1752738000886, "results": "75", "hashOfConfig": "72"}, {"size": 3263, "mtime": 1752725130297, "results": "76", "hashOfConfig": "72"}, {"size": 2855, "mtime": 1752636386518, "results": "77", "hashOfConfig": "72"}, {"size": 118, "mtime": 1752631801766, "results": "78", "hashOfConfig": "72"}, {"size": 2272, "mtime": 1752733627758, "results": "79", "hashOfConfig": "72"}, {"size": 3869, "mtime": 1752725130304, "results": "80", "hashOfConfig": "72"}, {"size": 384, "mtime": 1752738106347, "results": "81", "hashOfConfig": "72"}, {"size": 646, "mtime": 1752738209466, "results": "82", "hashOfConfig": "72"}, {"size": 403, "mtime": 1752738173227, "results": "83", "hashOfConfig": "72"}, {"size": 831, "mtime": 1752724658013, "results": "84", "hashOfConfig": "72"}, {"size": 384, "mtime": 1752738047861, "results": "85", "hashOfConfig": "72"}, {"size": 1669, "mtime": 1752725009012, "results": "86", "hashOfConfig": "72"}, {"size": 10410, "mtime": 1752725130305, "results": "87", "hashOfConfig": "72"}, {"size": 7112, "mtime": 1752630411600, "results": "88", "hashOfConfig": "72"}, {"size": 6816, "mtime": 1752725396687, "results": "89", "hashOfConfig": "72"}, {"size": 6574, "mtime": 1752725130307, "results": "90", "hashOfConfig": "72"}, {"size": 7306, "mtime": 1752725130308, "results": "91", "hashOfConfig": "72"}, {"size": 8528, "mtime": 1752630680961, "results": "92", "hashOfConfig": "72"}, {"size": 5663, "mtime": 1752630613051, "results": "93", "hashOfConfig": "72"}, {"size": 6298, "mtime": 1752725130309, "results": "94", "hashOfConfig": "72"}, {"size": 6833, "mtime": 1752631210590, "results": "95", "hashOfConfig": "72"}, {"size": 13632, "mtime": 1752733974241, "results": "96", "hashOfConfig": "72"}, {"size": 3456, "mtime": 1752631137486, "results": "97", "hashOfConfig": "72"}, {"size": 9906, "mtime": 1752631266732, "results": "98", "hashOfConfig": "72"}, {"size": 783, "mtime": 1752631349874, "results": "99", "hashOfConfig": "72"}, {"size": 3481, "mtime": 1752725130312, "results": "100", "hashOfConfig": "72"}, {"size": 10128, "mtime": 1752725130313, "results": "101", "hashOfConfig": "72"}, {"size": 746, "mtime": 1752630218135, "results": "102", "hashOfConfig": "72"}, {"size": 10542, "mtime": 1752724383823, "results": "103", "hashOfConfig": "72"}, {"size": 8115, "mtime": 1752630424162, "results": "104", "hashOfConfig": "72"}, {"size": 7093, "mtime": 1752725130316, "results": "105", "hashOfConfig": "72"}, {"size": 12468, "mtime": 1752725774665, "results": "106", "hashOfConfig": "72"}, {"size": 3454, "mtime": 1752630892026, "results": "107", "hashOfConfig": "72"}, {"size": 9016, "mtime": 1752725130319, "results": "108", "hashOfConfig": "72"}, {"size": 7042, "mtime": 1752725130331, "results": "109", "hashOfConfig": "72"}, {"size": 8132, "mtime": 1752724478795, "results": "110", "hashOfConfig": "72"}, {"size": 3387, "mtime": 1752631015554, "results": "111", "hashOfConfig": "72"}, {"size": 5595, "mtime": 1752725130333, "results": "112", "hashOfConfig": "72"}, {"size": 10626, "mtime": 1752725130353, "results": "113", "hashOfConfig": "72"}, {"size": 3423, "mtime": 1752630792387, "results": "114", "hashOfConfig": "72"}, {"size": 4396, "mtime": 1752630364422, "results": "115", "hashOfConfig": "72"}, {"size": 13118, "mtime": 1752725968851, "results": "116", "hashOfConfig": "72"}, {"size": 908, "mtime": 1752631469701, "results": "117", "hashOfConfig": "72"}, {"size": 6143, "mtime": 1752725477786, "results": "118", "hashOfConfig": "72"}, {"size": 5062, "mtime": 1752724686210, "results": "119", "hashOfConfig": "72"}, {"size": 2581, "mtime": 1752630003026, "results": "120", "hashOfConfig": "72"}, {"size": 1375, "mtime": 1752736508725, "results": "121", "hashOfConfig": "72"}, {"size": 2048, "mtime": 1752730793498, "results": "122", "hashOfConfig": "72"}, {"size": 1903, "mtime": 1752735770087, "results": "123", "hashOfConfig": "72"}, {"size": 3382, "mtime": 1752637321346, "results": "124", "hashOfConfig": "72"}, {"size": 7202, "mtime": 1752726060276, "results": "125", "hashOfConfig": "72"}, {"size": 848, "mtime": 1752736536295, "results": "126", "hashOfConfig": "72"}, {"size": 4573, "mtime": 1752725286038, "results": "127", "hashOfConfig": "72"}, {"size": 2145, "mtime": 1752631792628, "results": "128", "hashOfConfig": "72"}, {"size": 4793, "mtime": 1752637378512, "results": "129", "hashOfConfig": "72"}, {"size": 1414, "mtime": 1752631855457, "results": "130", "hashOfConfig": "72"}, {"size": 1596, "mtime": 1752736591051, "results": "131", "hashOfConfig": "72"}, {"size": 580, "mtime": 1752631892952, "results": "132", "hashOfConfig": "72"}, {"size": 756, "mtime": 1752736547862, "results": "133", "hashOfConfig": "72"}, {"size": 11825, "mtime": 1752636357720, "results": "134", "hashOfConfig": "72"}, {"size": 2331, "mtime": 1752725352347, "results": "135", "hashOfConfig": "72"}, {"size": 5530, "mtime": 1752630317122, "results": "136", "hashOfConfig": "72"}, {"size": 7817, "mtime": 1752725225246, "results": "137", "hashOfConfig": "72"}, {"size": 4137, "mtime": 1752725365475, "results": "138", "hashOfConfig": "72"}, {"size": 1377, "mtime": 1752725377401, "results": "139", "hashOfConfig": "72"}, {"size": 8766, "mtime": 1752636307379, "results": "140", "hashOfConfig": "72"}, {"size": 3472, "mtime": 1752636261135, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j1wvi9", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\blog\\[slug]\\page.tsx", [], ["352"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\manifest.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\packages\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\portfolio\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\forms\\contact-form.tsx", ["353"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cities.tsx", ["354"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\about-story.tsx", ["355"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-grid.tsx", ["356", "357", "358"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\blog-post-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-form-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-info.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\contact-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\gallery-showcase.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-grid.tsx", ["359"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\packages-preview.tsx", ["360"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-gallery.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\portfolio-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-grid.tsx", ["361", "362"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\services-overview.tsx", ["363"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\sections\\testimonials.tsx", [], ["364", "365", "366", "367"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\analytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\json-ld.tsx", [], ["368", "369", "370", "371", "372", "373", "374", "375", "376", "377", "378", "379"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\seo\\page-seo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\animated-element.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\image-modal.tsx", ["380"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\retry-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\scroll-to-top.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\skip-links.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\hooks\\use-api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\api-client.ts", [], ["381", "382", "383", "384"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\seo.ts", [], ["385", "386", "387", "388", "389", "390", "391"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\lib\\utils.ts", [], ["392", "393", "394"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\providers\\query-provider.tsx", [], ["395"], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali\\src\\types\\api.ts", [], [], {"ruleId": "396", "severity": 2, "message": "397", "line": 37, "column": 41, "nodeType": "398", "messageId": "399", "endLine": 37, "endColumn": 44, "suggestions": "400", "suppressions": "401"}, {"ruleId": "402", "severity": 1, "message": "403", "line": 51, "column": 27, "nodeType": null, "messageId": "404", "endLine": 51, "endColumn": 31}, {"ruleId": "402", "severity": 1, "message": "405", "line": 25, "column": 34, "nodeType": null, "messageId": "404", "endLine": 25, "endColumn": 39}, {"ruleId": "402", "severity": 1, "message": "405", "line": 124, "column": 31, "nodeType": null, "messageId": "404", "endLine": 124, "endColumn": 36}, {"ruleId": "402", "severity": 1, "message": "406", "line": 16, "column": 15, "nodeType": null, "messageId": "404", "endLine": 16, "endColumn": 19}, {"ruleId": "407", "severity": 1, "message": "408", "line": 30, "column": 9, "nodeType": "409", "endLine": 30, "endColumn": 39}, {"ruleId": "402", "severity": 1, "message": "405", "line": 208, "column": 34, "nodeType": null, "messageId": "404", "endLine": 208, "endColumn": 39}, {"ruleId": "402", "severity": 1, "message": "405", "line": 81, "column": 29, "nodeType": null, "messageId": "404", "endLine": 81, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "405", "line": 84, "column": 29, "nodeType": null, "messageId": "404", "endLine": 84, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "410", "line": 12, "column": 10, "nodeType": null, "messageId": "404", "endLine": 12, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "405", "line": 79, "column": 33, "nodeType": null, "messageId": "404", "endLine": 79, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "405", "line": 28, "column": 40, "nodeType": null, "messageId": "404", "endLine": 28, "endColumn": 45}, {"ruleId": "396", "severity": 2, "message": "397", "line": 145, "column": 41, "nodeType": "398", "messageId": "399", "endLine": 145, "endColumn": 44, "suggestions": "411", "suppressions": "412"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 149, "column": 47, "nodeType": "398", "messageId": "399", "endLine": 149, "endColumn": 50, "suggestions": "413", "suppressions": "414"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 235, "column": 41, "nodeType": "398", "messageId": "399", "endLine": 235, "endColumn": 44, "suggestions": "415", "suppressions": "416"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 239, "column": 47, "nodeType": "398", "messageId": "399", "endLine": 239, "endColumn": 50, "suggestions": "417", "suppressions": "418"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 150, "column": 30, "nodeType": "398", "messageId": "399", "endLine": 150, "endColumn": 33, "suggestions": "419", "suppressions": "420"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 151, "column": 33, "nodeType": "398", "messageId": "399", "endLine": 151, "endColumn": 36, "suggestions": "421", "suppressions": "422"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 154, "column": 28, "nodeType": "398", "messageId": "399", "endLine": 154, "endColumn": 31, "suggestions": "423", "suppressions": "424"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 164, "column": 35, "nodeType": "398", "messageId": "399", "endLine": 164, "endColumn": 38, "suggestions": "425", "suppressions": "426"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 165, "column": 34, "nodeType": "398", "messageId": "399", "endLine": 165, "endColumn": 37, "suggestions": "427", "suppressions": "428"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 166, "column": 27, "nodeType": "398", "messageId": "399", "endLine": 166, "endColumn": 30, "suggestions": "429", "suppressions": "430"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 166, "column": 74, "nodeType": "398", "messageId": "399", "endLine": 166, "endColumn": 77, "suggestions": "431", "suppressions": "432"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 167, "column": 56, "nodeType": "398", "messageId": "399", "endLine": 167, "endColumn": 59, "suggestions": "433", "suppressions": "434"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 170, "column": 60, "nodeType": "398", "messageId": "399", "endLine": 170, "endColumn": 63, "suggestions": "435", "suppressions": "436"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 172, "column": 30, "nodeType": "398", "messageId": "399", "endLine": 172, "endColumn": 33, "suggestions": "437", "suppressions": "438"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 173, "column": 36, "nodeType": "398", "messageId": "399", "endLine": 173, "endColumn": 39, "suggestions": "439", "suppressions": "440"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 174, "column": 31, "nodeType": "398", "messageId": "399", "endLine": 174, "endColumn": 34, "suggestions": "441", "suppressions": "442"}, {"ruleId": "407", "severity": 1, "message": "443", "line": 62, "column": 6, "nodeType": "444", "endLine": 62, "endColumn": 37, "suggestions": "445"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 9, "column": 19, "nodeType": "398", "messageId": "399", "endLine": 9, "endColumn": 22, "suggestions": "446", "suppressions": "447"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 70, "column": 38, "nodeType": "398", "messageId": "399", "endLine": 70, "endColumn": 41, "suggestions": "448", "suppressions": "449"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 75, "column": 37, "nodeType": "398", "messageId": "399", "endLine": 75, "endColumn": 40, "suggestions": "450", "suppressions": "451"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 85, "column": 57, "nodeType": "398", "messageId": "399", "endLine": 85, "endColumn": 60, "suggestions": "452", "suppressions": "453"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 179, "column": 26, "nodeType": "398", "messageId": "399", "endLine": 179, "endColumn": 29, "suggestions": "454", "suppressions": "455"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 183, "column": 33, "nodeType": "398", "messageId": "399", "endLine": 183, "endColumn": 36, "suggestions": "456", "suppressions": "457"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 186, "column": 30, "nodeType": "398", "messageId": "399", "endLine": 186, "endColumn": 33, "suggestions": "458", "suppressions": "459"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 187, "column": 33, "nodeType": "398", "messageId": "399", "endLine": 187, "endColumn": 36, "suggestions": "460", "suppressions": "461"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 194, "column": 24, "nodeType": "398", "messageId": "399", "endLine": 194, "endColumn": 27, "suggestions": "462", "suppressions": "463"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 195, "column": 31, "nodeType": "398", "messageId": "399", "endLine": 195, "endColumn": 34, "suggestions": "464", "suppressions": "465"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 200, "column": 57, "nodeType": "398", "messageId": "399", "endLine": 200, "endColumn": 60, "suggestions": "466", "suppressions": "467"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 59, "column": 46, "nodeType": "398", "messageId": "399", "endLine": 59, "endColumn": 49, "suggestions": "468", "suppressions": "469"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 59, "column": 56, "nodeType": "398", "messageId": "399", "endLine": 59, "endColumn": 59, "suggestions": "470", "suppressions": "471"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 100, "column": 93, "nodeType": "398", "messageId": "399", "endLine": 100, "endColumn": 96, "suggestions": "472", "suppressions": "473"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 18, "column": 42, "nodeType": "398", "messageId": "399", "endLine": 18, "endColumn": 45, "suggestions": "474", "suppressions": "475"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["476", "477"], ["478"], "@typescript-eslint/no-unused-vars", "'data' is defined but never used.", "unusedVar", "'index' is defined but never used.", "'Blog' is defined but never used.", "react-hooks/exhaustive-deps", "The 'blogs' logical expression could make the dependencies of useMemo Hook (at line 50) change on every render. To fix this, wrap the initialization of 'blogs' in its own useMemo() Hook.", "VariableDeclarator", "'formatPrice' is defined but never used.", ["479", "480"], ["481"], ["482", "483"], ["484"], ["485", "486"], ["487"], ["488", "489"], ["490"], ["491", "492"], ["493"], ["494", "495"], ["496"], ["497", "498"], ["499"], ["500", "501"], ["502"], ["503", "504"], ["505"], ["506", "507"], ["508"], ["509", "510"], ["511"], ["512", "513"], ["514"], ["515", "516"], ["517"], ["518", "519"], ["520"], ["521", "522"], ["523"], ["524", "525"], ["526"], "React Hook useEffect has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", "ArrayExpression", ["527"], ["528", "529"], ["530"], ["531", "532"], ["533"], ["534", "535"], ["536"], ["537", "538"], ["539"], ["540", "541"], ["542"], ["543", "544"], ["545"], ["546", "547"], ["548"], ["549", "550"], ["551"], ["552", "553"], ["554"], ["555", "556"], ["557"], ["558", "559"], ["560"], ["561", "562"], ["563"], ["564", "565"], ["566"], ["567", "568"], ["569"], ["570", "571"], ["572"], {"messageId": "573", "fix": "574", "desc": "575"}, {"messageId": "576", "fix": "577", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "581", "desc": "575"}, {"messageId": "576", "fix": "582", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "583", "desc": "575"}, {"messageId": "576", "fix": "584", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "585", "desc": "575"}, {"messageId": "576", "fix": "586", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "587", "desc": "575"}, {"messageId": "576", "fix": "588", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "589", "desc": "575"}, {"messageId": "576", "fix": "590", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "591", "desc": "575"}, {"messageId": "576", "fix": "592", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "593", "desc": "575"}, {"messageId": "576", "fix": "594", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "595", "desc": "575"}, {"messageId": "576", "fix": "596", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "597", "desc": "575"}, {"messageId": "576", "fix": "598", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "599", "desc": "575"}, {"messageId": "576", "fix": "600", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "601", "desc": "575"}, {"messageId": "576", "fix": "602", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "603", "desc": "575"}, {"messageId": "576", "fix": "604", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "605", "desc": "575"}, {"messageId": "576", "fix": "606", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "607", "desc": "575"}, {"messageId": "576", "fix": "608", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "609", "desc": "575"}, {"messageId": "576", "fix": "610", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "611", "desc": "575"}, {"messageId": "576", "fix": "612", "desc": "578"}, {"kind": "579", "justification": "580"}, {"desc": "613", "fix": "614"}, {"messageId": "573", "fix": "615", "desc": "575"}, {"messageId": "576", "fix": "616", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "617", "desc": "575"}, {"messageId": "576", "fix": "618", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "619", "desc": "575"}, {"messageId": "576", "fix": "620", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "621", "desc": "575"}, {"messageId": "576", "fix": "622", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "623", "desc": "575"}, {"messageId": "576", "fix": "624", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "625", "desc": "575"}, {"messageId": "576", "fix": "626", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "627", "desc": "575"}, {"messageId": "576", "fix": "628", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "629", "desc": "575"}, {"messageId": "576", "fix": "630", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "631", "desc": "575"}, {"messageId": "576", "fix": "632", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "633", "desc": "575"}, {"messageId": "576", "fix": "634", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "635", "desc": "575"}, {"messageId": "576", "fix": "636", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "637", "desc": "575"}, {"messageId": "576", "fix": "638", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "639", "desc": "575"}, {"messageId": "576", "fix": "640", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "641", "desc": "575"}, {"messageId": "576", "fix": "642", "desc": "578"}, {"kind": "579", "justification": "580"}, {"messageId": "573", "fix": "643", "desc": "575"}, {"messageId": "576", "fix": "644", "desc": "578"}, {"kind": "579", "justification": "580"}, "suggestUnknown", {"range": "645", "text": "646"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "647", "text": "648"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "649", "text": "646"}, {"range": "650", "text": "648"}, {"range": "651", "text": "646"}, {"range": "652", "text": "648"}, {"range": "653", "text": "646"}, {"range": "654", "text": "648"}, {"range": "655", "text": "646"}, {"range": "656", "text": "648"}, {"range": "657", "text": "646"}, {"range": "658", "text": "648"}, {"range": "659", "text": "646"}, {"range": "660", "text": "648"}, {"range": "661", "text": "646"}, {"range": "662", "text": "648"}, {"range": "663", "text": "646"}, {"range": "664", "text": "648"}, {"range": "665", "text": "646"}, {"range": "666", "text": "648"}, {"range": "667", "text": "646"}, {"range": "668", "text": "648"}, {"range": "669", "text": "646"}, {"range": "670", "text": "648"}, {"range": "671", "text": "646"}, {"range": "672", "text": "648"}, {"range": "673", "text": "646"}, {"range": "674", "text": "648"}, {"range": "675", "text": "646"}, {"range": "676", "text": "648"}, {"range": "677", "text": "646"}, {"range": "678", "text": "648"}, {"range": "679", "text": "646"}, {"range": "680", "text": "648"}, "Update the dependencies array to be: [isOpen, currentIndex, onClose, goToPrevious, goToNext]", {"range": "681", "text": "682"}, {"range": "683", "text": "646"}, {"range": "684", "text": "648"}, {"range": "685", "text": "646"}, {"range": "686", "text": "648"}, {"range": "687", "text": "646"}, {"range": "688", "text": "648"}, {"range": "689", "text": "646"}, {"range": "690", "text": "648"}, {"range": "691", "text": "646"}, {"range": "692", "text": "648"}, {"range": "693", "text": "646"}, {"range": "694", "text": "648"}, {"range": "695", "text": "646"}, {"range": "696", "text": "648"}, {"range": "697", "text": "646"}, {"range": "698", "text": "648"}, {"range": "699", "text": "646"}, {"range": "700", "text": "648"}, {"range": "701", "text": "646"}, {"range": "702", "text": "648"}, {"range": "703", "text": "646"}, {"range": "704", "text": "648"}, {"range": "705", "text": "646"}, {"range": "706", "text": "648"}, {"range": "707", "text": "646"}, {"range": "708", "text": "648"}, {"range": "709", "text": "646"}, {"range": "710", "text": "648"}, {"range": "711", "text": "646"}, {"range": "712", "text": "648"}, [1014, 1017], "unknown", [1014, 1017], "never", [6455, 6458], [6455, 6458], [6737, 6740], [6737, 6740], [10621, 10624], [10621, 10624], [10912, 10915], [10912, 10915], [4887, 4890], [4887, 4890], [4931, 4934], [4931, 4934], [5023, 5026], [5023, 5026], [5327, 5330], [5327, 5330], [5378, 5381], [5378, 5381], [5420, 5423], [5420, 5423], [5467, 5470], [5467, 5470], [5587, 5590], [5587, 5590], [5721, 5724], [5721, 5724], [5775, 5778], [5775, 5778], [5841, 5844], [5841, 5844], [5886, 5889], [5886, 5889], [1418, 1449], "[is<PERSON><PERSON>, currentIndex, onClose, goToPrevious, goToNext]", [326, 329], [326, 329], [1546, 1549], [1546, 1549], [1703, 1706], [1703, 1706], [1995, 1998], [1995, 1998], [6002, 6005], [6002, 6005], [6111, 6114], [6111, 6114], [6191, 6194], [6191, 6194], [6237, 6240], [6237, 6240], [6385, 6388], [6385, 6388], [6426, 6429], [6426, 6429], [6604, 6607], [6604, 6607], [1745, 1748], [1745, 1748], [1755, 1758], [1755, 1758], [3003, 3006], [3003, 3006], [735, 738], [735, 738]]