"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{8292:(e,s,a)=>{a.d(s,{A:()=>N});var l=a(5155),r=a(2115),t=a(2177),i=a(221),d=a(8309),n=a(2486),c=a(9420),o=a(1366),m=a(285),x=a(9434);let h=r.forwardRef((e,s)=>{let{className:a,type:r,...t}=e;return(0,l.jsx)("input",{type:r,className:(0,x.cn)("flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});h.displayName="Input";let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("textarea",{className:(0,x.cn)("flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});u.displayName="Textarea";var p=a(6695),j=a(6126),b=a(1710);let g=d.Ik({name:d.Yj().min(2,"Name must be at least 2 characters"),email:d.Yj().email("Please enter a valid email address"),phone:d.Yj().min(10,"Please enter a valid phone number"),service:d.Yj().min(1,"Please select a service"),date:d.Yj().min(1,"Please select a preferred date"),message:d.Yj().min(10,"Message must be at least 10 characters")}),f=["Bridal Makeup","Party Makeup","Engagement Makeup","Traditional Makeup","Photoshoot Makeup","Makeup Lessons","Other"];function N(){let[e,s]=(0,r.useState)(!1),[a,d]=(0,r.useState)(!1),N=(0,b.Q2)(),{register:y,handleSubmit:v,formState:{errors:w},reset:k}=(0,t.mN)({resolver:(0,i.u)(g)}),M=async()=>{s(!0);try{await new Promise(e=>setTimeout(e,2e3)),d(!0),k()}catch(e){console.error("Error submitting form:",e)}finally{s(!1)}},S=(0,x.ec)(N.contact.whatsapp,N.whatsappMessage);return a?(0,l.jsx)(p.Zp,{className:"max-w-md mx-auto text-center",children:(0,l.jsxs)(p.Wu,{className:"pt-6",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)(n.A,{className:"w-8 h-8 text-green-600"})}),(0,l.jsx)("h3",{className:"font-display text-xl font-semibold text-text-primary mb-2",children:"Message Sent Successfully!"}),(0,l.jsx)("p",{className:"text-text-secondary mb-6",children:"Thank you for your inquiry. We'll get back to you within 24 hours."}),(0,l.jsx)(m.$,{onClick:()=>d(!1),variant:"outline",className:"w-full",children:"Send Another Message"})]})}):(0,l.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,l.jsxs)(p.Zp,{children:[(0,l.jsxs)(p.aR,{children:[(0,l.jsx)(p.ZB,{className:"font-display text-2xl",children:"Send us a Message"}),(0,l.jsx)(p.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("form",{onSubmit:v(M),className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"name",className:"text-sm font-medium text-text-primary",children:"Full Name *"}),(0,l.jsx)(h,{id:"name",placeholder:"Your full name",...y("name"),className:w.name?"border-red-500":""}),w.name&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.name.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-text-primary",children:"Email Address *"}),(0,l.jsx)(h,{id:"email",type:"email",placeholder:"<EMAIL>",...y("email"),className:w.email?"border-red-500":""}),w.email&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.email.message})]})]}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-text-primary",children:"Phone Number *"}),(0,l.jsx)(h,{id:"phone",placeholder:"+977-9800000000",...y("phone"),className:w.phone?"border-red-500":""}),w.phone&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.phone.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"date",className:"text-sm font-medium text-text-primary",children:"Preferred Date *"}),(0,l.jsx)(h,{id:"date",type:"date",...y("date"),className:w.date?"border-red-500":""}),w.date&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.date.message})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"service",className:"text-sm font-medium text-text-primary",children:"Service Interested In *"}),(0,l.jsxs)("select",{id:"service",...y("service"),className:"flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ".concat(w.service?"border-red-500":""),children:[(0,l.jsx)("option",{value:"",children:"Select a service"}),f.map(e=>(0,l.jsx)("option",{value:e,children:e},e))]}),w.service&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.service.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"message",className:"text-sm font-medium text-text-primary",children:"Message *"}),(0,l.jsx)(u,{id:"message",placeholder:"Tell us about your requirements, occasion, and any specific preferences...",rows:4,...y("message"),className:w.message?"border-red-500":""}),w.message&&(0,l.jsx)("p",{className:"text-red-500 text-xs",children:w.message.message})]}),(0,l.jsx)(m.$,{type:"submit",variant:"gradient",size:"lg",className:"w-full",disabled:e,children:e?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})})]})})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(p.Zp,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,l.jsxs)(p.aR,{children:[(0,l.jsxs)(p.ZB,{className:"font-display text-xl flex items-center gap-2",children:[(0,l.jsx)(c.A,{className:"w-5 h-5 text-rose-gold-dark"}),"Quick Contact"]}),(0,l.jsx)(p.BT,{children:"Need immediate assistance? Contact us directly via WhatsApp or phone."})]}),(0,l.jsxs)(p.Wu,{className:"space-y-4",children:[(0,l.jsx)(m.$,{asChild:!0,variant:"gradient",size:"lg",className:"w-full",children:(0,l.jsxs)("a",{href:S,target:"_blank",rel:"noopener noreferrer",children:[(0,l.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"WhatsApp Now"]})}),(0,l.jsx)(m.$,{asChild:!0,variant:"outline",size:"lg",className:"w-full",children:(0,l.jsxs)("a",{href:"tel:".concat(N.contact.phone),children:[(0,l.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Call Now"]})})]})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsx)(p.ZB,{className:"font-display text-xl",children:"Business Hours"})}),(0,l.jsxs)(p.Wu,{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-text-secondary",children:"Monday - Saturday"}),(0,l.jsx)(j.E,{variant:"outline",children:"9:00 AM - 6:00 PM"})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,l.jsx)(j.E,{variant:"outline",children:"10:00 AM - 4:00 PM"})]}),(0,l.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,l.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice"})})]})]})]})]})}}}]);