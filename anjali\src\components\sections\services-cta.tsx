'use client'

import Link from 'next/link'
import { ArrowRight, Phone, MessageCircle, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function ServicesCTA() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    "Hi! I&apos;d like to book a makeup service. Could you help me choose the right service for my occasion?"
  )

  return (
    <Section background="gradient">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight">
            Ready to Book Your
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Perfect Look?
            </span>
          </h2>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto">
            Contact us today to discuss your makeup needs and book your appointment. 
            We&apos;re here to make you look and feel absolutely stunning.
          </p>
        </AnimatedElement>

        {/* CTA Buttons */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="gradient" className="group">
              <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="w-5 h-5 mr-2" />
                WhatsApp Booking
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button asChild size="lg" variant="outline">
              <Link href="/contact">
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Consultation
              </Link>
            </Button>
          </div>
        </AnimatedElement>

        {/* Quick Info Cards */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                  <Phone className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Quick Response</h3>
                <p className="text-text-secondary text-sm">
                  We typically respond to inquiries within 2 hours during business hours
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Flexible Scheduling</h3>
                <p className="text-text-secondary text-sm">
                  Early morning and evening appointments available for your convenience
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Free Consultation</h3>
                <p className="text-text-secondary text-sm">
                  Complimentary consultation to discuss your vision and preferences
                </p>
              </CardContent>
            </Card>
          </div>
        </AnimatedElement>

        {/* Additional Links */}
        <AnimatedElement animation="slideUp" delay={0.7}>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/packages" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              View Service Packages
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/portfolio" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              See Our Portfolio
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/about" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              About Our Artist
            </Link>
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
