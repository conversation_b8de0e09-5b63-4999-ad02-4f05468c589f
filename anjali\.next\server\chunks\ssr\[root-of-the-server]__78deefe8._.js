module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "cn": ()=>cn,
    "debounce": ()=>debounce,
    "formatDate": ()=>formatDate,
    "formatPrice": ()=>formatPrice,
    "generateSEOSchema": ()=>generateSEOSchema,
    "generateWhatsAppLink": ()=>generateWhatsAppLink,
    "getImageUrl": ()=>getImageUrl,
    "getReadingTime": ()=>getReadingTime,
    "isInViewport": ()=>isInViewport,
    "scrollToElement": ()=>scrollToElement,
    "slugify": ()=>slugify,
    "truncateText": ()=>truncateText,
    "validateEmail": ()=>validateEmail,
    "validatePhone": ()=>validatePhone
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatPrice(price) {
    return price.replace(/NPR\s*/g, 'NPR ');
}
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function generateWhatsAppLink(phone, message) {
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
}
function slugify(text) {
    return text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/[\s_-]+/g, '-').replace(/^-+|-+$/g, '');
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
function getImageUrl(imagePath) {
    // Handle placeholder images for development
    if (imagePath.startsWith('/images/')) {
        return `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=600&fit=crop&crop=face`;
    }
    return imagePath;
}
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function validatePhone(phone) {
    const phoneRegex = /^(\+977)?[0-9]{10}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function scrollToElement(elementId, offset = 80) {
    const element = document.getElementById(elementId);
    if (element) {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);
}
function getReadingTime(content) {
    const wordsPerMinute = 200;
    const words = content.trim().split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
}
function generateSEOSchema(type, data) {
    const baseSchema = {
        '@context': 'https://schema.org',
        '@type': type
    };
    switch(type){
        case 'LocalBusiness':
            return {
                ...baseSchema,
                name: data.name,
                description: data.description,
                url: data.url,
                telephone: data.phone,
                address: {
                    '@type': 'PostalAddress',
                    streetAddress: data.address.street,
                    addressLocality: data.address.city,
                    addressRegion: data.address.state,
                    addressCountry: data.address.country,
                    postalCode: data.address.zipCode
                },
                geo: data.geo,
                openingHours: data.openingHours,
                priceRange: data.priceRange,
                serviceArea: data.serviceArea
            };
        case 'Article':
            return {
                ...baseSchema,
                headline: data.title,
                description: data.description,
                author: {
                    '@type': 'Person',
                    name: data.author
                },
                datePublished: data.publishedAt,
                dateModified: data.updatedAt,
                image: data.image,
                url: data.url
            };
        default:
            return baseSchema;
    }
}
}),
"[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Button": ()=>Button,
    "buttonVariants": ()=>buttonVariants
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0", {
    variants: {
        variant: {
            default: "bg-rose-gold text-white shadow hover:bg-rose-gold-dark",
            destructive: "bg-red-500 text-white shadow-sm hover:bg-red-600",
            outline: "border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white",
            secondary: "bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark",
            ghost: "hover:bg-rose-gold-light hover:text-rose-gold-dark",
            link: "text-rose-gold-dark underline-offset-4 hover:underline",
            gradient: "bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark"
        },
        size: {
            default: "h-9 px-4 py-2",
            sm: "h-8 rounded-md px-3 text-xs",
            lg: "h-10 rounded-md px-8",
            xl: "h-12 rounded-lg px-10 text-base",
            icon: "h-9 w-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
const Button = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"](({ className, variant, size, asChild = false, ...props }, ref)=>{
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 46,
        columnNumber: 7
    }, ("TURBOPACK compile-time value", void 0));
});
Button.displayName = "Button";
;
}),
"[project]/src/data/services.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"services\":[{\"id\":\"bridal-makeup\",\"title\":\"Bridal Makeup\",\"description\":\"Complete bridal transformation with traditional and modern looks\",\"features\":[\"Pre-bridal consultation\",\"Trial makeup session\",\"Wedding day makeup\",\"Touch-up kit included\",\"Hair styling available\"],\"duration\":\"3-4 hours\",\"price\":\"Starting from NPR 15,000\",\"image\":\"/images/services/bridal-makeup.jpg\",\"category\":\"bridal\",\"popular\":true},{\"id\":\"party-makeup\",\"title\":\"Party & Event Makeup\",\"description\":\"Glamorous looks for special occasions and celebrations\",\"features\":[\"Customized look consultation\",\"Professional makeup application\",\"Long-lasting formula\",\"Photo-ready finish\"],\"duration\":\"1.5-2 hours\",\"price\":\"Starting from NPR 5,000\",\"image\":\"/images/services/party-makeup.jpg\",\"category\":\"party\",\"popular\":true},{\"id\":\"engagement-makeup\",\"title\":\"Engagement Makeup\",\"description\":\"Perfect look for your special engagement ceremony\",\"features\":[\"Elegant and sophisticated styling\",\"Traditional or contemporary looks\",\"Complementary hair styling\",\"Photography-friendly makeup\"],\"duration\":\"2-3 hours\",\"price\":\"Starting from NPR 8,000\",\"image\":\"/images/services/engagement-makeup.jpg\",\"category\":\"engagement\",\"popular\":false},{\"id\":\"photoshoot-makeup\",\"title\":\"Photoshoot Makeup\",\"description\":\"Professional makeup for modeling and photography sessions\",\"features\":[\"HD makeup techniques\",\"Multiple look changes\",\"Professional lighting consideration\",\"Touch-ups during shoot\"],\"duration\":\"2-4 hours\",\"price\":\"Starting from NPR 6,000\",\"image\":\"/images/services/photoshoot-makeup.jpg\",\"category\":\"photoshoot\",\"popular\":false},{\"id\":\"traditional-makeup\",\"title\":\"Traditional Nepali Makeup\",\"description\":\"Authentic traditional looks for cultural ceremonies\",\"features\":[\"Traditional color schemes\",\"Cultural authenticity\",\"Festival and ceremony appropriate\",\"Traditional jewelry styling advice\"],\"duration\":\"2-3 hours\",\"price\":\"Starting from NPR 7,000\",\"image\":\"/images/services/traditional-makeup.jpg\",\"category\":\"traditional\",\"popular\":false},{\"id\":\"makeup-lessons\",\"title\":\"Personal Makeup Lessons\",\"description\":\"Learn professional makeup techniques for everyday use\",\"features\":[\"One-on-one instruction\",\"Personalized techniques\",\"Product recommendations\",\"Practice session included\"],\"duration\":\"2 hours\",\"price\":\"Starting from NPR 4,000\",\"image\":\"/images/services/makeup-lessons.jpg\",\"category\":\"lessons\",\"popular\":false}]}"));}),
"[project]/src/data/packages.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"packages\":[{\"id\":\"complete-bridal\",\"title\":\"Complete Bridal Package\",\"description\":\"Everything you need for your perfect wedding day\",\"services\":[\"Bridal makeup trial\",\"Wedding day makeup\",\"Hair styling\",\"Saree draping\",\"Touch-up kit\"],\"duration\":\"Full day (6-8 hours)\",\"originalPrice\":\"NPR 25,000\",\"discountedPrice\":\"NPR 20,000\",\"savings\":\"NPR 5,000\",\"image\":\"/images/packages/complete-bridal.jpg\",\"popular\":true,\"badge\":\"Most Popular\"},{\"id\":\"engagement-special\",\"title\":\"Engagement Special\",\"description\":\"Perfect package for your engagement ceremony\",\"services\":[\"Engagement makeup\",\"Hair styling\",\"Jewelry consultation\",\"Photography tips\"],\"duration\":\"3-4 hours\",\"originalPrice\":\"NPR 12,000\",\"discountedPrice\":\"NPR 10,000\",\"savings\":\"NPR 2,000\",\"image\":\"/images/packages/engagement-special.jpg\",\"popular\":false,\"badge\":null},{\"id\":\"party-glam\",\"title\":\"Party Glam Package\",\"description\":\"Glamorous look for parties and special events\",\"services\":[\"Party makeup\",\"Hair styling\",\"Outfit consultation\",\"Touch-up products\"],\"duration\":\"2-3 hours\",\"originalPrice\":\"NPR 8,000\",\"discountedPrice\":\"NPR 6,500\",\"savings\":\"NPR 1,500\",\"image\":\"/images/packages/party-glam.jpg\",\"popular\":true,\"badge\":\"Best Value\"},{\"id\":\"photoshoot-pro\",\"title\":\"Professional Photoshoot\",\"description\":\"Complete package for professional photography sessions\",\"services\":[\"HD makeup\",\"Multiple look changes\",\"Hair styling variations\",\"On-set touch-ups\"],\"duration\":\"4-6 hours\",\"originalPrice\":\"NPR 15,000\",\"discountedPrice\":\"NPR 12,000\",\"savings\":\"NPR 3,000\",\"image\":\"/images/packages/photoshoot-pro.jpg\",\"popular\":false,\"badge\":null},{\"id\":\"monthly-beauty\",\"title\":\"Monthly Beauty Package\",\"description\":\"Regular beauty maintenance with exclusive discounts\",\"services\":[\"3 makeup sessions\",\"Beauty consultation\",\"Product recommendations\",\"Priority booking\"],\"duration\":\"Monthly package\",\"originalPrice\":\"NPR 18,000\",\"discountedPrice\":\"NPR 15,000\",\"savings\":\"NPR 3,000\",\"image\":\"/images/packages/monthly-beauty.jpg\",\"popular\":false,\"badge\":\"Subscription\"}]}"));}),
"[project]/src/data/testimonials.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"testimonials\":[{\"id\":\"testimonial-1\",\"name\":\"Priya Sharma\",\"location\":\"Biratnagar\",\"service\":\"Bridal Makeup\",\"rating\":5,\"text\":\"Anjali made my wedding day absolutely perfect! Her attention to detail and professional approach made me feel like a princess. The makeup lasted the entire day and looked stunning in all our photos.\",\"image\":\"/images/testimonials/priya-sharma.jpg\",\"date\":\"2024-06-15\",\"featured\":true},{\"id\":\"testimonial-2\",\"name\":\"Sita Rai\",\"location\":\"Itahari\",\"service\":\"Party Makeup\",\"rating\":5,\"text\":\"I was amazed by Anjali's skill and creativity. She understood exactly what I wanted and delivered beyond my expectations. Highly recommend her services!\",\"image\":\"/images/testimonials/sita-rai.jpg\",\"date\":\"2024-05-20\",\"featured\":true},{\"id\":\"testimonial-3\",\"name\":\"Kamala Thapa\",\"location\":\"Dharan\",\"service\":\"Engagement Makeup\",\"rating\":5,\"text\":\"Professional, punctual, and incredibly talented. Anjali made my engagement ceremony memorable with her beautiful makeup artistry. Thank you so much!\",\"image\":\"/images/testimonials/kamala-thapa.jpg\",\"date\":\"2024-04-10\",\"featured\":false},{\"id\":\"testimonial-4\",\"name\":\"Sunita Limbu\",\"location\":\"Damak\",\"service\":\"Traditional Makeup\",\"rating\":5,\"text\":\"Anjali perfectly captured the traditional Nepali look I wanted for our cultural ceremony. Her knowledge of traditional makeup techniques is impressive.\",\"image\":\"/images/testimonials/sunita-limbu.jpg\",\"date\":\"2024-03-25\",\"featured\":false},{\"id\":\"testimonial-5\",\"name\":\"Rashmi Gurung\",\"location\":\"Kathmandu\",\"service\":\"Photoshoot Makeup\",\"rating\":5,\"text\":\"Working with Anjali for my portfolio photoshoot was amazing. She understood the lighting and camera requirements perfectly. The results were outstanding!\",\"image\":\"/images/testimonials/rashmi-gurung.jpg\",\"date\":\"2024-02-14\",\"featured\":true},{\"id\":\"testimonial-6\",\"name\":\"Mina Shrestha\",\"location\":\"Inaruwa\",\"service\":\"Makeup Lessons\",\"rating\":5,\"text\":\"The personal makeup lesson was so helpful! Anjali taught me techniques that I use every day now. She's patient and explains everything clearly.\",\"image\":\"/images/testimonials/mina-shrestha.jpg\",\"date\":\"2024-01-30\",\"featured\":false},{\"id\":\"testimonial-7\",\"name\":\"Gita Poudel\",\"location\":\"Jhapa\",\"service\":\"Bridal Makeup\",\"rating\":5,\"text\":\"Anjali is truly an artist! She made me feel so confident and beautiful on my wedding day. All my guests were asking about my makeup artist. Highly recommended!\",\"image\":\"/images/testimonials/gita-poudel.jpg\",\"date\":\"2024-01-15\",\"featured\":false}]}"));}),
"[project]/src/data/gallery.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"gallery\":[{\"id\":\"gallery-1\",\"title\":\"Bridal Elegance\",\"description\":\"Traditional bridal makeup with modern touches\",\"image\":\"/images/gallery/bridal-1.jpg\",\"category\":\"bridal\",\"featured\":true,\"tags\":[\"bridal\",\"traditional\",\"elegant\"]},{\"id\":\"gallery-2\",\"title\":\"Contemporary Bride\",\"description\":\"Modern bridal look with subtle glamour\",\"image\":\"/images/gallery/bridal-2.jpg\",\"category\":\"bridal\",\"featured\":true,\"tags\":[\"bridal\",\"modern\",\"glamour\"]},{\"id\":\"gallery-3\",\"title\":\"Party Glam\",\"description\":\"Bold and beautiful party makeup\",\"image\":\"/images/gallery/party-1.jpg\",\"category\":\"party\",\"featured\":false,\"tags\":[\"party\",\"bold\",\"glamour\"]},{\"id\":\"gallery-4\",\"title\":\"Engagement Glow\",\"description\":\"Soft and romantic engagement makeup\",\"image\":\"/images/gallery/engagement-1.jpg\",\"category\":\"engagement\",\"featured\":true,\"tags\":[\"engagement\",\"romantic\",\"soft\"]},{\"id\":\"gallery-5\",\"title\":\"Traditional Beauty\",\"description\":\"Authentic Nepali traditional makeup\",\"image\":\"/images/gallery/traditional-1.jpg\",\"category\":\"traditional\",\"featured\":false,\"tags\":[\"traditional\",\"cultural\",\"authentic\"]},{\"id\":\"gallery-6\",\"title\":\"Photoshoot Ready\",\"description\":\"Professional makeup for photography\",\"image\":\"/images/gallery/photoshoot-1.jpg\",\"category\":\"photoshoot\",\"featured\":false,\"tags\":[\"photoshoot\",\"professional\",\"hd\"]},{\"id\":\"gallery-7\",\"title\":\"Festival Vibes\",\"description\":\"Colorful makeup for festival celebrations\",\"image\":\"/images/gallery/festival-1.jpg\",\"category\":\"traditional\",\"featured\":false,\"tags\":[\"festival\",\"colorful\",\"celebration\"]},{\"id\":\"gallery-8\",\"title\":\"Evening Elegance\",\"description\":\"Sophisticated evening party makeup\",\"image\":\"/images/gallery/party-2.jpg\",\"category\":\"party\",\"featured\":true,\"tags\":[\"party\",\"evening\",\"sophisticated\"]},{\"id\":\"gallery-9\",\"title\":\"Bridal Perfection\",\"description\":\"Flawless bridal makeup with intricate details\",\"image\":\"/images/gallery/bridal-3.jpg\",\"category\":\"bridal\",\"featured\":false,\"tags\":[\"bridal\",\"flawless\",\"detailed\"]},{\"id\":\"gallery-10\",\"title\":\"Natural Glow\",\"description\":\"Subtle and natural everyday makeup\",\"image\":\"/images/gallery/natural-1.jpg\",\"category\":\"natural\",\"featured\":false,\"tags\":[\"natural\",\"subtle\",\"everyday\"]},{\"id\":\"gallery-11\",\"title\":\"Dramatic Eyes\",\"description\":\"Bold eye makeup for special occasions\",\"image\":\"/images/gallery/dramatic-1.jpg\",\"category\":\"party\",\"featured\":false,\"tags\":[\"dramatic\",\"eyes\",\"bold\"]},{\"id\":\"gallery-12\",\"title\":\"Vintage Charm\",\"description\":\"Classic vintage-inspired makeup look\",\"image\":\"/images/gallery/vintage-1.jpg\",\"category\":\"party\",\"featured\":false,\"tags\":[\"vintage\",\"classic\",\"retro\"]}],\"categories\":[{\"id\":\"all\",\"name\":\"All\",\"count\":12},{\"id\":\"bridal\",\"name\":\"Bridal\",\"count\":3},{\"id\":\"party\",\"name\":\"Party\",\"count\":4},{\"id\":\"engagement\",\"name\":\"Engagement\",\"count\":1},{\"id\":\"traditional\",\"name\":\"Traditional\",\"count\":2},{\"id\":\"photoshoot\",\"name\":\"Photoshoot\",\"count\":1},{\"id\":\"natural\",\"name\":\"Natural\",\"count\":1}]}"));}),
"[project]/src/data/blog.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"posts\":[{\"id\":\"bridal-makeup-trends-2024\",\"title\":\"Top Bridal Makeup Trends in Nepal for 2024\",\"slug\":\"bridal-makeup-trends-2024\",\"excerpt\":\"Discover the latest bridal makeup trends that are taking Nepal by storm this year. From traditional to contemporary looks.\",\"content\":\"blog-content/bridal-makeup-trends-2024.md\",\"author\":\"Anjali Makeup Artist\",\"publishedAt\":\"2024-06-01\",\"updatedAt\":\"2024-06-01\",\"featured\":true,\"image\":\"/images/blog/bridal-trends-2024.jpg\",\"tags\":[\"bridal\",\"trends\",\"2024\",\"nepal\"],\"category\":\"Bridal Tips\",\"readTime\":\"5 min read\",\"seo\":{\"metaTitle\":\"Top Bridal Makeup Trends in Nepal 2024 | Anjali Makeup Artist\",\"metaDescription\":\"Explore the latest bridal makeup trends in Nepal for 2024. Expert tips from professional makeup artist in Biratnagar.\",\"keywords\":[\"bridal makeup Nepal\",\"makeup trends 2024\",\"wedding makeup Biratnagar\"]}},{\"id\":\"choosing-right-makeup-skin-tone\",\"title\":\"How to Choose the Right Makeup for Your Skin Tone\",\"slug\":\"choosing-right-makeup-skin-tone\",\"excerpt\":\"Learn how to select makeup colors that complement your unique skin tone for a flawless, natural look.\",\"content\":\"blog-content/choosing-right-makeup-skin-tone.md\",\"author\":\"Anjali Makeup Artist\",\"publishedAt\":\"2024-05-15\",\"updatedAt\":\"2024-05-15\",\"featured\":false,\"image\":\"/images/blog/skin-tone-makeup.jpg\",\"tags\":[\"makeup tips\",\"skin tone\",\"color matching\"],\"category\":\"Beauty Tips\",\"readTime\":\"4 min read\",\"seo\":{\"metaTitle\":\"Choose Right Makeup for Your Skin Tone | Expert Tips\",\"metaDescription\":\"Professional makeup artist guide to selecting perfect makeup colors for your skin tone. Get flawless results every time.\",\"keywords\":[\"makeup skin tone\",\"color matching makeup\",\"makeup tips Nepal\"]}},{\"id\":\"traditional-nepali-makeup-guide\",\"title\":\"Traditional Nepali Makeup: A Complete Guide\",\"slug\":\"traditional-nepali-makeup-guide\",\"excerpt\":\"Explore the rich heritage of traditional Nepali makeup techniques and learn how to achieve authentic cultural looks.\",\"content\":\"blog-content/traditional-nepali-makeup-guide.md\",\"author\":\"Anjali Makeup Artist\",\"publishedAt\":\"2024-04-20\",\"updatedAt\":\"2024-04-20\",\"featured\":true,\"image\":\"/images/blog/traditional-nepali-makeup.jpg\",\"tags\":[\"traditional\",\"nepali\",\"cultural\",\"heritage\"],\"category\":\"Cultural Beauty\",\"readTime\":\"6 min read\",\"seo\":{\"metaTitle\":\"Traditional Nepali Makeup Guide | Cultural Beauty Tips\",\"metaDescription\":\"Complete guide to traditional Nepali makeup techniques. Learn authentic cultural beauty practices from expert makeup artist.\",\"keywords\":[\"traditional Nepali makeup\",\"cultural makeup Nepal\",\"heritage beauty\"]}},{\"id\":\"makeup-longevity-tips\",\"title\":\"10 Tips to Make Your Makeup Last All Day\",\"slug\":\"makeup-longevity-tips\",\"excerpt\":\"Professional secrets to ensure your makeup stays fresh and beautiful from morning to night.\",\"content\":\"blog-content/makeup-longevity-tips.md\",\"author\":\"Anjali Makeup Artist\",\"publishedAt\":\"2024-03-10\",\"updatedAt\":\"2024-03-10\",\"featured\":false,\"image\":\"/images/blog/long-lasting-makeup.jpg\",\"tags\":[\"makeup tips\",\"longevity\",\"professional\"],\"category\":\"Beauty Tips\",\"readTime\":\"3 min read\",\"seo\":{\"metaTitle\":\"10 Tips for Long-Lasting Makeup | Professional Secrets\",\"metaDescription\":\"Expert makeup artist shares 10 proven tips to make your makeup last all day. Professional techniques for lasting beauty.\",\"keywords\":[\"long lasting makeup\",\"makeup tips\",\"professional makeup\"]}},{\"id\":\"seasonal-makeup-trends\",\"title\":\"Seasonal Makeup Trends: Adapting Your Look\",\"slug\":\"seasonal-makeup-trends\",\"excerpt\":\"Discover how to adapt your makeup routine for different seasons and weather conditions in Nepal.\",\"content\":\"blog-content/seasonal-makeup-trends.md\",\"author\":\"Anjali Makeup Artist\",\"publishedAt\":\"2024-02-25\",\"updatedAt\":\"2024-02-25\",\"featured\":false,\"image\":\"/images/blog/seasonal-makeup.jpg\",\"tags\":[\"seasonal\",\"trends\",\"weather\",\"adaptation\"],\"category\":\"Seasonal Beauty\",\"readTime\":\"4 min read\",\"seo\":{\"metaTitle\":\"Seasonal Makeup Trends Nepal | Weather-Appropriate Beauty\",\"metaDescription\":\"Learn how to adapt your makeup for different seasons in Nepal. Expert tips for weather-appropriate beauty looks.\",\"keywords\":[\"seasonal makeup Nepal\",\"weather makeup tips\",\"makeup trends\"]}}],\"categories\":[{\"id\":\"bridal-tips\",\"name\":\"Bridal Tips\",\"count\":1},{\"id\":\"beauty-tips\",\"name\":\"Beauty Tips\",\"count\":2},{\"id\":\"cultural-beauty\",\"name\":\"Cultural Beauty\",\"count\":1},{\"id\":\"seasonal-beauty\",\"name\":\"Seasonal Beauty\",\"count\":1}]}"));}),
"[project]/src/data/site-config.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"site\":{\"name\":\"Anjali Makeup Artist\",\"tagline\":\"Professional Makeup Artist in Biratnagar, Nepal\",\"description\":\"Expert makeup artist serving Biratnagar, Itahari, Dharan, Damak, Inaruwa, Jhapa, and Kathmandu. Specializing in bridal, party, and traditional makeup with over 5 years of experience.\",\"url\":\"https://anjalimakeup.com\",\"logo\":\"/images/logo.png\",\"favicon\":\"/favicon.ico\"},\"contact\":{\"phone\":\"+977-9800000000\",\"whatsapp\":\"+977-9800000000\",\"email\":\"<EMAIL>\",\"address\":{\"street\":\"Main Road, Biratnagar\",\"city\":\"Biratnagar\",\"state\":\"Province 1\",\"country\":\"Nepal\",\"zipCode\":\"56613\"},\"workingHours\":{\"monday\":\"9:00 AM - 6:00 PM\",\"tuesday\":\"9:00 AM - 6:00 PM\",\"wednesday\":\"9:00 AM - 6:00 PM\",\"thursday\":\"9:00 AM - 6:00 PM\",\"friday\":\"9:00 AM - 6:00 PM\",\"saturday\":\"9:00 AM - 6:00 PM\",\"sunday\":\"10:00 AM - 4:00 PM\"}},\"social\":{\"facebook\":\"https://facebook.com/anjalimakeup\",\"instagram\":\"https://instagram.com/anjalimakeup\",\"tiktok\":\"https://tiktok.com/@anjalimakeup\",\"youtube\":\"https://youtube.com/@anjalimakeup\"},\"serviceAreas\":[{\"name\":\"Biratnagar\",\"primary\":true,\"travelFee\":0},{\"name\":\"Itahari\",\"primary\":false,\"travelFee\":500},{\"name\":\"Dharan\",\"primary\":false,\"travelFee\":800},{\"name\":\"Damak\",\"primary\":false,\"travelFee\":600},{\"name\":\"Inaruwa\",\"primary\":false,\"travelFee\":400},{\"name\":\"Jhapa\",\"primary\":false,\"travelFee\":700},{\"name\":\"Kathmandu\",\"primary\":false,\"travelFee\":2000}],\"whatsappMessage\":\"Hi! I'm interested in booking a makeup session. Could you please provide more details about your services and availability?\",\"seo\":{\"defaultTitle\":\"Anjali Makeup Artist | Professional Makeup Services in Biratnagar, Nepal\",\"defaultDescription\":\"Professional makeup artist in Biratnagar offering bridal, party, and traditional makeup services. Serving Itahari, Dharan, Damak, Inaruwa, Jhapa, and Kathmandu.\",\"keywords\":[\"makeup artist Biratnagar\",\"bridal makeup Nepal\",\"makeup artist Itahari\",\"makeup artist Dharan\",\"professional makeup Nepal\",\"wedding makeup artist\",\"party makeup Biratnagar\",\"traditional makeup Nepal\"],\"author\":\"Anjali Makeup Artist\",\"twitterHandle\":\"@anjalimakeup\"},\"analytics\":{\"googleAnalyticsId\":\"G-XXXXXXXXXX\"}}"));}),
"[project]/src/lib/data.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getBlogCategories": ()=>getBlogCategories,
    "getBlogPost": ()=>getBlogPost,
    "getBlogPosts": ()=>getBlogPosts,
    "getBlogPostsByCategory": ()=>getBlogPostsByCategory,
    "getContactInfo": ()=>getContactInfo,
    "getFeaturedBlogPosts": ()=>getFeaturedBlogPosts,
    "getFeaturedGalleryItems": ()=>getFeaturedGalleryItems,
    "getFeaturedTestimonials": ()=>getFeaturedTestimonials,
    "getGalleryCategories": ()=>getGalleryCategories,
    "getGalleryItems": ()=>getGalleryItems,
    "getGalleryItemsByCategory": ()=>getGalleryItemsByCategory,
    "getPackage": ()=>getPackage,
    "getPackages": ()=>getPackages,
    "getPopularPackages": ()=>getPopularPackages,
    "getPopularServices": ()=>getPopularServices,
    "getRelatedBlogPosts": ()=>getRelatedBlogPosts,
    "getSEODefaults": ()=>getSEODefaults,
    "getService": ()=>getService,
    "getServiceAreas": ()=>getServiceAreas,
    "getServices": ()=>getServices,
    "getServicesByCategory": ()=>getServicesByCategory,
    "getSiteConfig": ()=>getSiteConfig,
    "getSocialLinks": ()=>getSocialLinks,
    "getTestimonials": ()=>getTestimonials,
    "getTestimonialsByService": ()=>getTestimonialsByService,
    "getWhatsAppMessage": ()=>getWhatsAppMessage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/services.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$packages$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/packages.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$testimonials$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/testimonials.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/gallery.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/blog.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/site-config.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
function getServices() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__["default"].services;
}
function getService(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__["default"].services.find((service)=>service.id === id);
}
function getPopularServices() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__["default"].services.filter((service)=>service.popular);
}
function getServicesByCategory(category) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__["default"].services.filter((service)=>service.category === category);
}
function getPackages() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$packages$2e$json__$28$json$29$__["default"].packages;
}
function getPackage(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$packages$2e$json__$28$json$29$__["default"].packages.find((pkg)=>pkg.id === id);
}
function getPopularPackages() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$packages$2e$json__$28$json$29$__["default"].packages.filter((pkg)=>pkg.popular);
}
function getTestimonials() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$testimonials$2e$json__$28$json$29$__["default"].testimonials;
}
function getFeaturedTestimonials() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$testimonials$2e$json__$28$json$29$__["default"].testimonials.filter((testimonial)=>testimonial.featured);
}
function getTestimonialsByService(service) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$testimonials$2e$json__$28$json$29$__["default"].testimonials.filter((testimonial)=>testimonial.service.toLowerCase().includes(service.toLowerCase()));
}
function getGalleryItems() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__["default"].gallery;
}
function getFeaturedGalleryItems() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__["default"].gallery.filter((item)=>item.featured);
}
function getGalleryItemsByCategory(category) {
    if (category === 'all') return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__["default"].gallery;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__["default"].gallery.filter((item)=>item.category === category);
}
function getGalleryCategories() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__["default"].categories;
}
function getBlogPosts() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].posts;
}
function getBlogPost(slug) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].posts.find((post)=>post.slug === slug);
}
function getFeaturedBlogPosts() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].posts.filter((post)=>post.featured);
}
function getBlogPostsByCategory(category) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].posts.filter((post)=>post.category.toLowerCase().replace(/\s+/g, '-') === category.toLowerCase());
}
function getBlogCategories() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].categories;
}
function getRelatedBlogPosts(currentPost, limit = 3) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__["default"].posts.filter((post)=>post.id !== currentPost.id && (post.category === currentPost.category || post.tags.some((tag)=>currentPost.tags.includes(tag)))).slice(0, limit);
}
function getSiteConfig() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"];
}
function getContactInfo() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"].contact;
}
function getSocialLinks() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"].social;
}
function getServiceAreas() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"].serviceAreas;
}
function getWhatsAppMessage() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"].whatsappMessage;
}
function getSEODefaults() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__["default"].seo;
}
;
}),
"[project]/src/lib/data.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$services$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/services.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$packages$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/packages.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$testimonials$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/testimonials.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$gallery$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/gallery.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$blog$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/blog.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$site$2d$config$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/site-config.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-ssr] (ecmascript) <locals>");
}),
"[project]/src/components/layout/header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Header
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-ssr] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone.js [app-ssr] (ecmascript) <export default as Phone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/instagram.js [app-ssr] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/facebook.js [app-ssr] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
const navigation = [
    {
        name: 'Home',
        href: '/'
    },
    {
        name: 'About',
        href: '/about'
    },
    {
        name: 'Services',
        href: '/services'
    },
    {
        name: 'Packages',
        href: '/packages'
    },
    {
        name: 'Portfolio',
        href: '/portfolio'
    },
    {
        name: 'Blog',
        href: '/blog'
    },
    {
        name: 'Contact',
        href: '/contact'
    }
];
function Header() {
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isScrolled, setIsScrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSiteConfig"])();
    const socialLinks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSocialLinks"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleScroll = ()=>{
            setIsScrolled(window.scrollY > 10);
        };
        window.addEventListener('scroll', handleScroll);
        return ()=>window.removeEventListener('scroll', handleScroll);
    }, []);
    const whatsappLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateWhatsAppLink"])(siteConfig.contact.whatsapp, siteConfig.whatsappMessage);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('fixed top-0 left-0 right-0 z-50 transition-all duration-300', isScrolled ? 'bg-white/95 backdrop-blur-md shadow-md' : 'bg-white'),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                id: "navigation",
                className: "mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-16 items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-white font-display font-bold text-lg",
                                            children: "A"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 57,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/header.tsx",
                                        lineNumber: 56,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-display text-xl font-semibold text-text-primary",
                                        children: siteConfig.site.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/header.tsx",
                                        lineNumber: 59,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 54,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "hidden md:block",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-10 flex items-baseline space-x-4",
                                children: navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: item.href,
                                        className: "text-text-primary hover:text-rose-gold-dark px-3 py-2 rounded-md text-sm font-medium transition-colors",
                                        children: item.name
                                    }, item.name, false, {
                                        fileName: "[project]/src/components/layout/header.tsx",
                                        lineNumber: 69,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 66,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "hidden md:flex items-center space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            href: socialLinks.instagram,
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            className: "text-text-secondary hover:text-rose-gold-dark transition-colors",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"], {
                                                className: "h-5 w-5"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 89,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 83,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            href: socialLinks.facebook,
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            className: "text-text-secondary hover:text-rose-gold-dark transition-colors",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"], {
                                                className: "h-5 w-5"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 97,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            href: socialLinks.tiktok,
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            className: "text-text-secondary hover:text-rose-gold-dark transition-colors",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiTiktokLogo"], {
                                                className: "h-5 w-5"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 105,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 99,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "gradient",
                                    size: "sm",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: whatsappLink,
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 110,
                                                columnNumber: 17
                                            }, this),
                                            "Book Now"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/layout/header.tsx",
                                        lineNumber: 109,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 108,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "md:hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: ()=>setIsOpen(!isOpen),
                                "aria-label": "Toggle menu",
                                children: isOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-6 w-6"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 124,
                                    columnNumber: 25
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                                    className: "h-6 w-6"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 124,
                                    columnNumber: 53
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 118,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/header.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/header.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/header.tsx",
                lineNumber: 51,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    initial: {
                        opacity: 0,
                        height: 0
                    },
                    animate: {
                        opacity: 1,
                        height: 'auto'
                    },
                    exit: {
                        opacity: 0,
                        height: 0
                    },
                    transition: {
                        duration: 0.3
                    },
                    className: "md:hidden bg-white border-t border-gray-200",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "px-2 pt-2 pb-3 space-y-1 sm:px-3",
                        children: [
                            navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: item.href,
                                    className: "text-text-primary hover:text-rose-gold-dark block px-3 py-2 rounded-md text-base font-medium transition-colors",
                                    onClick: ()=>setIsOpen(false),
                                    children: item.name
                                }, item.name, false, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 142,
                                    columnNumber: 17
                                }, this)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "pt-4 pb-2 border-t border-gray-200",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between px-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: socialLinks.instagram,
                                                    target: "_blank",
                                                    rel: "noopener noreferrer",
                                                    className: "text-text-secondary hover:text-rose-gold-dark transition-colors",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"], {
                                                        className: "h-6 w-6"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/layout/header.tsx",
                                                        lineNumber: 160,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/header.tsx",
                                                    lineNumber: 154,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: socialLinks.facebook,
                                                    target: "_blank",
                                                    rel: "noopener noreferrer",
                                                    className: "text-text-secondary hover:text-rose-gold-dark transition-colors",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"], {
                                                        className: "h-6 w-6"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/layout/header.tsx",
                                                        lineNumber: 168,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/header.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 153,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            asChild: true,
                                            variant: "gradient",
                                            size: "sm",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: whatsappLink,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                                        className: "h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/layout/header.tsx",
                                                        lineNumber: 173,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Book Now"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/layout/header.tsx",
                                                lineNumber: 172,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/header.tsx",
                                            lineNumber: 171,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/header.tsx",
                                    lineNumber: 152,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/header.tsx",
                                lineNumber: 151,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/header.tsx",
                        lineNumber: 140,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/header.tsx",
                    lineNumber: 133,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/header.tsx",
                lineNumber: 131,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/header.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/components/seo/analytics.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Analytics
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/script.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/data.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
function Analytics() {
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSiteConfig"])();
    const gaId = siteConfig.analytics.googleAnalyticsId;
    // Only load analytics in production
    if ("TURBOPACK compile-time truthy", 1) {
        return null;
    }
    //TURBOPACK unreachable
    ;
}
}),
"[project]/src/components/ui/scroll-to-top.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ScrollToTop
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up.js [app-ssr] (ecmascript) <export default as ArrowUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function ScrollToTop() {
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const toggleVisibility = ()=>{
            if (window.pageYOffset > 300) {
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }
        };
        window.addEventListener('scroll', toggleVisibility);
        return ()=>window.removeEventListener('scroll', toggleVisibility);
    }, []);
    const scrollToTop = ()=>{
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 0,
                scale: 0.8
            },
            animate: {
                opacity: 1,
                scale: 1
            },
            exit: {
                opacity: 0,
                scale: 0.8
            },
            transition: {
                duration: 0.2
            },
            className: "fixed bottom-8 right-8 z-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                onClick: scrollToTop,
                size: "icon",
                variant: "gradient",
                className: "w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300",
                "aria-label": "Scroll to top",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUp$3e$__["ArrowUp"], {
                    className: "w-5 h-5"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/scroll-to-top.tsx",
                    lineNumber: 48,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/scroll-to-top.tsx",
                lineNumber: 41,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/scroll-to-top.tsx",
            lineNumber: 34,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/scroll-to-top.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/providers/query-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "QueryProvider": ()=>QueryProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
/* eslint-disable @typescript-eslint/no-explicit-any */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function QueryProvider({ children }) {
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
            defaultOptions: {
                queries: {
                    // With SSR, we usually want to set some default staleTime
                    // above 0 to avoid refetching immediately on the client
                    staleTime: 60 * 1000,
                    gcTime: 10 * 60 * 1000,
                    retry: (failureCount, error)=>{
                        // Don't retry on 4xx errors
                        if (error?.status >= 400 && error?.status < 500) {
                            return false;
                        }
                        // Retry up to 3 times for other errors
                        return failureCount < 3;
                    },
                    refetchOnWindowFocus: false,
                    refetchOnMount: true,
                    refetchOnReconnect: true
                },
                mutations: {
                    retry: false
                }
            }
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false
            }, void 0, false, {
                fileName: "[project]/src/providers/query-provider.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/providers/query-provider.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__78deefe8._.js.map