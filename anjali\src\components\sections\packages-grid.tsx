'use client'

import Image from 'next/image'
import Link from 'next/link'
import { <PERSON>, <PERSON>, <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Tag, Loader2, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useActivePackages, useContactInfo } from '@/hooks/use-api'
import { generateWhatsAppLink } from '@/lib/utils'
import { getSiteConfig } from '@/lib/data'

export default function PackagesGrid() {
  const { data: packagesData, isLoading, error } = useActivePackages()
  const { data: contactInfo } = useContactInfo()
  const siteConfig = getSiteConfig()

  const packages = packagesData?.packages || []

  // Loading state
  if (isLoading) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Service Packages"
            title="Complete Makeup Packages"
            description="Specially curated packages combining our most popular services at exceptional value. Perfect for brides, special events, and those who want the complete experience."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-text-secondary">Loading packages...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Service Packages"
            title="Complete Makeup Packages"
            description="Specially curated packages combining our most popular services at exceptional value. Perfect for brides, special events, and those who want the complete experience."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Unable to Load Packages
          </h3>
          <p className="text-text-secondary mb-6">
            We&apos;re having trouble loading our packages. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Packages"
          title="Complete Beauty Solutions"
          description="Carefully curated packages that combine multiple services for maximum value and convenience. Perfect for special occasions and regular beauty maintenance."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {packages.map((pkg, index) => (
          <StaggeredItem key={pkg.id}>
            <Card className={`group h-full transition-all duration-300 hover:shadow-2xl border-0 overflow-hidden ${
              pkg.popular ? 'ring-2 ring-rose-gold/30' : ''
            }`}>
              {/* Package Image */}
              <div className="relative aspect-[16/9] overflow-hidden">
                <Image
                  src={pkg.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=400&fit=crop&crop=face&q=80`}
                  alt={pkg.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-2">
                      {pkg.popular && (
                        <Badge
                          variant="default"
                          className="text-xs"
                        >
                          <Sparkles className="w-3 h-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Top Badge */}
                {pkg.popular && (
                  <div className="absolute top-4 right-4">
                    <Badge
                      variant="default"
                      className="text-xs bg-white/90 text-text-primary"
                    >
                      <Sparkles className="w-3 h-3 mr-1" />
                      Popular
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-3">
                <CardTitle className="text-xl group-hover:text-rose-gold-dark transition-colors">
                  {pkg.name}
                </CardTitle>
                <CardDescription className="text-text-secondary">
                  {pkg.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6 flex-1 flex flex-col">
                {/* Pricing */}
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <div className="text-3xl font-bold text-text-primary">
                      {pkg.price}
                    </div>
                    {pkg.originalPrice && (
                      <div className="text-lg text-text-muted line-through">
                        {pkg.originalPrice}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {pkg.originalPrice && (
                      <Badge variant="success" className="text-xs">
                        <Tag className="w-3 h-3 mr-1" />
                        Save Money
                      </Badge>
                    )}
                    {pkg.duration && (
                      <div className="flex items-center gap-2 text-text-secondary text-sm">
                        <Clock className="w-4 h-4" />
                        <span>{pkg.duration}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Services Included */}
                <div className="space-y-3 flex-1">
                  <h4 className="font-semibold text-text-primary">Package Includes:</h4>
                  <ul className="space-y-2">
                    {pkg.services.map((service, idx) => (
                      <li key={idx} className="text-sm text-text-secondary flex items-start gap-3">
                        <Check className="w-4 h-4 text-rose-gold-dark mt-0.5 flex-shrink-0" />
                        <span>{service}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* CTA Button */}
                <div className="pt-4 mt-auto">
                  <Button 
                    asChild 
                    variant={pkg.popular ? "gradient" : "outline"} 
                    className="w-full group"
                    size="lg"
                  >
                    <Link
                      href={generateWhatsAppLink(
                        contactInfo?.phone || '',
                        `Hi! I&apos;m interested in the ${pkg.name} package. Could you provide more details about booking and availability?`
                      )}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Book This Package
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Package Benefits */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <Tag className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Best Value</h3>
              <p className="text-text-secondary text-sm">Save up to 25% compared to individual services</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blush-pink/10 to-lavender/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                <Check className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Complete Solution</h3>
              <p className="text-text-secondary text-sm">Everything you need for your special occasion</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-lavender/10 to-rose-gold/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Time Efficient</h3>
              <p className="text-text-secondary text-sm">Coordinated services save time and effort</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Premium Experience</h3>
              <p className="text-text-secondary text-sm">Enhanced service with priority booking</p>
            </CardContent>
          </Card>
        </div>
      </AnimatedElement>

      {/* Custom Package Option */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-12">
        <Card className="bg-gradient-to-br from-cream to-soft-gray border-0">
          <CardContent className="p-8 text-center">
            <h3 className="font-display text-2xl font-semibold text-text-primary mb-4">
              Need a Custom Package?
            </h3>
            <p className="text-text-secondary mb-6 max-w-2xl mx-auto">
              Don&apos;t see exactly what you&apos;re looking for? We can create a custom package 
              tailored to your specific needs and budget. Contact us to discuss your requirements.
            </p>
            <Button asChild variant="outline" size="lg">
              <Link 
                href={generateWhatsAppLink(
                  siteConfig.contact.whatsapp,
                  "Hi! I&apos;d like to discuss creating a custom makeup package for my needs. Could you help me with this?"
                )}
                target="_blank" 
                rel="noopener noreferrer"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Create Custom Package
              </Link>
            </Button>
          </CardContent>
        </Card>
      </AnimatedElement>
    </Section>
  )
}
