/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://anjalimakeup.com',
  generateRobotsTxt: false, // We have a custom robots.txt
  generateIndexSitemap: true,
  exclude: [
    '/admin/*',
    '/api/*',
    '/private/*',
    '/_next/*',
    '/static/*',
    '/404',
    '/500',
    '/sitemap.xml',
    '/robots.txt'
  ],
  additionalPaths: async (config) => {
    const result = []

    // Add blog posts with more comprehensive data
    const blogPosts = [
      {
        slug: 'bridal-makeup-trends-2024',
        lastmod: '2024-01-15T00:00:00.000Z',
        priority: 0.8
      },
      {
        slug: 'choosing-right-makeup-skin-tone',
        lastmod: '2024-01-10T00:00:00.000Z',
        priority: 0.7
      },
      {
        slug: 'traditional-nepali-makeup-guide',
        lastmod: '2024-01-05T00:00:00.000Z',
        priority: 0.8
      },
      {
        slug: 'makeup-longevity-tips',
        lastmod: '2024-01-01T00:00:00.000Z',
        priority: 0.7
      },
      {
        slug: 'seasonal-makeup-trends',
        lastmod: '2023-12-28T00:00:00.000Z',
        priority: 0.6
      }
    ]

    blogPosts.forEach((post) => {
      result.push({
        loc: `/blog/${post.slug}`,
        changefreq: 'weekly',
        priority: post.priority,
        lastmod: post.lastmod,
      })
    })

    // Add service pages if they exist
    const services = [
      'bridal-makeup',
      'party-makeup',
      'traditional-makeup',
      'photoshoot-makeup',
      'special-occasion-makeup'
    ]

    services.forEach((service) => {
      result.push({
        loc: `/services/${service}`,
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: new Date().toISOString(),
      })
    })

    // Add package pages if they exist
    const packages = [
      'bridal-complete-package',
      'party-glam-package',
      'traditional-ceremony-package'
    ]

    packages.forEach((pkg) => {
      result.push({
        loc: `/packages/${pkg}`,
        changefreq: 'weekly',
        priority: 0.6,
        lastmod: new Date().toISOString(),
      })
    })

    return result
  },
  transform: async (config, path) => {
    // Custom priority and changefreq for different pages
    const customConfig = {
      '/': {
        priority: 1.0,
        changefreq: 'daily',
        lastmod: new Date().toISOString()
      },
      '/about': {
        priority: 0.9,
        changefreq: 'monthly',
        lastmod: new Date().toISOString()
      },
      '/services': {
        priority: 0.9,
        changefreq: 'weekly',
        lastmod: new Date().toISOString()
      },
      '/packages': {
        priority: 0.8,
        changefreq: 'weekly',
        lastmod: new Date().toISOString()
      },
      '/portfolio': {
        priority: 0.8,
        changefreq: 'weekly',
        lastmod: new Date().toISOString()
      },
      '/blog': {
        priority: 0.7,
        changefreq: 'daily',
        lastmod: new Date().toISOString()
      },
      '/contact': {
        priority: 0.6,
        changefreq: 'monthly',
        lastmod: new Date().toISOString()
      },
    }

    const pageConfig = customConfig[path] || {
      priority: 0.5,
      changefreq: 'monthly',
      lastmod: new Date().toISOString()
    }

    return {
      loc: path,
      changefreq: pageConfig.changefreq,
      priority: pageConfig.priority,
      lastmod: pageConfig.lastmod,
      alternateRefs: [
        {
          href: `${config.siteUrl}${path}`,
          hreflang: 'en',
        },
        {
          href: `${config.siteUrl}/ne${path}`, // If you add Nepali language support
          hreflang: 'ne',
        },
      ],
    }
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/admin/', '/api/', '/private/'],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: ['/admin/', '/api/', '/private/'],
      },
    ],
    additionalSitemaps: [
      `${process.env.SITE_URL || 'https://anjalimakeup.com'}/sitemap.xml`,
    ],
  },
}
