'use client'

import Link from 'next/link'
import { <PERSON>R<PERSON>, MessageCircle, Calendar, Gift } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function PackagesCTA() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    "Hi! I&apos;m interested in booking one of your makeup packages. Could you help me choose the best option for my needs?"
  )

  return (
    <Section background="gradient">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight">
            Ready to Save with Our
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Amazing Packages?
            </span>
          </h2>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto">
            Book your package today and enjoy premium makeup services at unbeatable value. 
            Perfect for brides, special events, and beauty enthusiasts.
          </p>
        </AnimatedElement>

        {/* CTA Buttons */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="gradient" className="group">
              <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="w-5 h-5 mr-2" />
                Book Package Now
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button asChild size="lg" variant="outline">
              <Link href="/contact">
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Consultation
              </Link>
            </Button>
          </div>
        </AnimatedElement>

        {/* Package Benefits */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                  <Gift className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Best Value Guarantee</h3>
                <p className="text-text-secondary text-sm">
                  Our packages offer the best value compared to individual services
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Priority Booking</h3>
                <p className="text-text-secondary text-sm">
                  Package clients get priority scheduling and dedicated time slots
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Dedicated Support</h3>
                <p className="text-text-secondary text-sm">
                  Personal consultation and ongoing support throughout your package
                </p>
              </CardContent>
            </Card>
          </div>
        </AnimatedElement>

        {/* Additional Information */}
        <AnimatedElement animation="slideUp" delay={0.7}>
          <Card className="bg-white/60 backdrop-blur-sm border-0 max-w-3xl mx-auto">
            <CardContent className="p-6">
              <h3 className="font-display text-lg font-semibold text-text-primary mb-4 text-center">
                Package Terms & Benefits
              </h3>
              <div className="grid md:grid-cols-2 gap-6 text-sm text-text-secondary">
                <div>
                  <h4 className="font-semibold text-text-primary mb-2">What&apos;s Included:</h4>
                  <ul className="space-y-1">
                    <li>• All listed services in the package</li>
                    <li>• Professional consultation</li>
                    <li>• Touch-up kit for events</li>
                    <li>• Priority booking status</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-text-primary mb-2">Booking Information:</h4>
                  <ul className="space-y-1">
                    <li>• Advance booking recommended</li>
                    <li>• Flexible payment options available</li>
                    <li>• Package validity: 6 months</li>
                    <li>• Transferable to family members</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </AnimatedElement>

        {/* Additional Links */}
        <AnimatedElement animation="slideUp" delay={0.9}>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/services" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              View Individual Services
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/portfolio" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              See Our Work
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/about" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              About Our Artist
            </Link>
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
