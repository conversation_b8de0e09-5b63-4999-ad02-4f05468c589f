{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/app/manifest.ts"], "sourcesContent": ["import { MetadataRoute } from 'next'\n\nexport default function manifest(): MetadataRoute.Manifest {\n  return {\n    name: '<PERSON><PERSON><PERSON>up Artist',\n    short_name: '<PERSON><PERSON><PERSON>',\n    description: 'Professional makeup artist serving Biratnagar, Itahari, Dharan, and surrounding areas.',\n    start_url: '/',\n    display: 'standalone',\n    background_color: '#ffffff',\n    theme_color: '#f8f9fa',\n    orientation: 'portrait',\n    scope: '/',\n    lang: 'en',\n    categories: ['beauty', 'lifestyle', 'business'],\n    icons: [\n      {\n        src: '/favicon.ico',\n        sizes: 'any',\n        type: 'image/x-icon',\n      },\n      {\n        src: '/icon-192.png',\n        sizes: '192x192',\n        type: 'image/png',\n      },\n      {\n        src: '/icon-512.png',\n        sizes: '512x512',\n        type: 'image/png',\n      },\n      {\n        src: '/apple-touch-icon.png',\n        sizes: '180x180',\n        type: 'image/png',\n      },\n    ],\n    screenshots: [\n      {\n        src: '/screenshot-wide.png',\n        sizes: '1280x720',\n        type: 'image/png',\n        form_factor: 'wide',\n        label: '<PERSON><PERSON><PERSON> Makeup Artist Homepage',\n      },\n      {\n        src: '/screenshot-narrow.png',\n        sizes: '750x1334',\n        type: 'image/png',\n        form_factor: 'narrow',\n        label: 'Anjali Makeup Artist Mobile View',\n      },\n    ],\n    shortcuts: [\n      {\n        name: 'Book Appointment',\n        short_name: 'Book',\n        description: 'Book a makeup appointment',\n        url: '/contact',\n        icons: [\n          {\n            src: '/icon-book.png',\n            sizes: '96x96',\n            type: 'image/png',\n          },\n        ],\n      },\n      {\n        name: 'View Portfolio',\n        short_name: 'Portfolio',\n        description: 'Browse makeup portfolio',\n        url: '/portfolio',\n        icons: [\n          {\n            src: '/icon-portfolio.png',\n            sizes: '96x96',\n            type: 'image/png',\n          },\n        ],\n      },\n      {\n        name: 'Services',\n        short_name: 'Services',\n        description: 'View makeup services',\n        url: '/services',\n        icons: [\n          {\n            src: '/icon-services.png',\n            sizes: '96x96',\n            type: 'image/png',\n          },\n        ],\n      },\n    ],\n  }\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACtB,OAAO;QACL,MAAM;QACN,YAAY;QACZ,aAAa;QACb,WAAW;QACX,SAAS;QACT,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,OAAO;QACP,MAAM;QACN,YAAY;YAAC;YAAU;YAAa;SAAW;QAC/C,OAAO;YACL;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;SACD;QACD,aAAa;YACX;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;SACD;QACD,WAAW;YACT;gBACE,MAAM;gBACN,YAAY;gBACZ,aAAa;gBACb,KAAK;gBACL,OAAO;oBACL;wBACE,KAAK;wBACL,OAAO;wBACP,MAAM;oBACR;iBACD;YACH;YACA;gBACE,MAAM;gBACN,YAAY;gBACZ,aAAa;gBACb,KAAK;gBACL,OAAO;oBACL;wBACE,KAAK;wBACL,OAAO;wBACP,MAAM;oBACR;iBACD;YACH;YACA;gBACE,MAAM;gBACN,YAAY;gBACZ,aAAa;gBACb,KAAK;gBACL,OAAO;oBACL;wBACE,KAAK;wBACL,OAAO;wBACP,MAAM;oBACR;iBACD;YACH;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/app/manifest--route-entry.js"], "sourcesContent": ["            import { NextResponse } from 'next/server'\n            import handler from \"./manifest.ts\"\n            import { resolveRouteData } from\n'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\n            const contentType = \"application/manifest+json\"\n            const cacheControl = \"public, max-age=0, must-revalidate\"\n            const fileType = \"manifest\"\n\n            if (typeof handler !== 'function') {\n                throw new Error('Default export is missing in \"./manifest.ts\"')\n            }\n\n            export async function GET() {\n              const data = await handler()\n              const content = resolveRouteData(data, fileType)\n\n              return new NextResponse(content, {\n                headers: {\n                  'Content-Type': contentType,\n                  'Cache-Control': cacheControl,\n                },\n              })\n            }\n\n            export * from \"./manifest.ts\"\n        "], "names": [], "mappings": ";;;AAAY;AACA;AACA;;;;AAGA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,sHAAA,CAAA,UAAO,KAAK,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD;IACzB,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}